import numpy as np
import soundfile as sf
import pyloudnorm as pyln
import logging
from pedalboard.io import AudioFile
import tempfile
import os

logger = logging.getLogger(__name__)

def calculate_lufs(audio_data, sample_rate, window_size=1.0):
    """Calculate LUFS (Loudness Units Full Scale) for audio data"""
    try:
        # Create a meter
        meter = pyln.Meter(sample_rate)
        
        # Calculate LUFS
        if is_stereo(audio_data):
            # For stereo, use as is
            lufs = meter.integrated_loudness(audio_data.T)
        else:
            # For mono, reshape to match expected format
            lufs = meter.integrated_loudness(audio_data)
        
        # Calculate short-term LUFS for graphing
        hop_size = int(sample_rate * 0.1)  # 100ms hop size
        window_samples = int(sample_rate * window_size)
        
        times = []
        lufs_values = []
        
        for i in range(0, len(audio_data[0]) if is_stereo(audio_data) else len(audio_data), hop_size):
            if is_stereo(audio_data):
                chunk = audio_data[:, i:i+window_samples]
                if chunk.shape[1] < window_samples / 2:  # Skip small chunks
                    continue
                chunk_lufs = meter.integrated_loudness(chunk.T)
            else:
                chunk = audio_data[i:i+window_samples]
                if len(chunk) < window_samples / 2:  # Skip small chunks
                    continue
                chunk_lufs = meter.integrated_loudness(chunk)
            
            if not np.isnan(chunk_lufs) and not np.isinf(chunk_lufs):
                times.append(i / sample_rate)
                lufs_values.append(chunk_lufs)
        
        # Format data for Chart.js
        graph_data = {
            "times": times,
            "lufs": lufs_values
        }
        
        # Return just the LUFS value, not a tuple
        return lufs
    except Exception as e:
        logger.error(f"Error calculating LUFS: {e}")
        raise

def create_lufs_graph(audio_data, sample_rate):
    """
    Create data for a LUFS graph.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        
    Returns:
        dict: Graph data with time and LUFS values, or None on error
    """
    try:
        # Create a meter
        meter = pyln.Meter(sample_rate)
        
        # Calculate LUFS for each second
        segment_duration = 1.0  # 1 second segments
        segment_samples = int(segment_duration * sample_rate)
        
        times = []
        lufs_values = []
        
        # Handle mono vs stereo
        is_stereo_audio = is_stereo(audio_data)
        
        # Calculate LUFS for each segment
        for i in range(0, len(audio_data[0]) if is_stereo_audio else len(audio_data), segment_samples):
            if is_stereo_audio:
                segment = audio_data[:, i:i+segment_samples]
                if segment.shape[1] < segment_samples / 2:  # Skip small segments
                    continue
                segment_lufs = meter.integrated_loudness(segment.T)
            else:
                segment = audio_data[i:i+segment_samples]
                if len(segment) < segment_samples / 2:  # Skip small segments
                    continue
                segment_lufs = meter.integrated_loudness(segment)
            
            # Only add valid LUFS values
            if not np.isnan(segment_lufs) and not np.isinf(segment_lufs) and segment_lufs > -100:
                times.append(float(i / sample_rate))
                lufs_values.append(float(segment_lufs))
        
        # Ensure we have at least some data points
        if not times or not lufs_values:
            # If no valid segments, add a single point
            times = [0.0]
            lufs_values = [-70.0]
        
        return {
            'times': times,
            'lufs': lufs_values
        }
        
    except Exception as e:
        logger.error(f"Error creating LUFS graph: {e}", exc_info=True)
        return None

def normalize_audio_loudness(audio_data, sample_rate, target_lufs):
    """
    Normalize audio to a target LUFS level.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        target_lufs: Target LUFS level
        
    Returns:
        numpy.ndarray: Normalized audio data, or None on error
    """
    try:
        # Handle mono vs stereo
        is_mono = len(audio_data.shape) == 1 or audio_data.shape[1] == 1
        
        # Reshape mono audio for pyloudnorm
        if is_mono and len(audio_data.shape) == 1:
            data_for_meter = audio_data.reshape(-1, 1)
        else:
            data_for_meter = audio_data
        
        # Measure the current loudness
        meter = pyln.Meter(sample_rate)
        current_lufs = meter.integrated_loudness(data_for_meter)
        logger.info(f"Current LUFS: {current_lufs}, Target LUFS: {target_lufs}")
        
        # Handle very quiet audio (can cause assertion errors)
        if current_lufs < -70:
            logger.warning("Audio is too quiet for normalization, using default gain")
            # Apply a fixed gain instead
            gain_db = target_lufs + 70  # Rough estimate
            normalized_audio = audio_data * 10**(gain_db/20)
        else:
            # Normalize to target LUFS
            try:
                normalized_audio = pyln.normalize.loudness(audio_data, current_lufs, target_lufs)
            except AssertionError:
                logger.warning("Assertion error during normalization, using manual gain calculation")
                # Calculate gain manually
                gain_db = target_lufs - current_lufs
                normalized_audio = audio_data * 10**(gain_db/20)
        
        return normalized_audio
    except Exception as e:
        logger.error(f"Error normalizing audio: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def apply_compression(audio_data, sample_rate, threshold_db=-20, ratio=4, attack_ms=5, release_ms=100):
    """
    Applies compression to audio data.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        threshold_db: Threshold in dB where compression begins
        ratio: Compression ratio
        attack_ms: Attack time in milliseconds
        release_ms: Release time in milliseconds
        
    Returns:
        numpy.ndarray: Compressed audio data, or None on error
    """
    logger.info(f"Starting compression with threshold={threshold_db}, ratio={ratio}, attack={attack_ms}, release={release_ms}")
    
    try:
        from pedalboard import Pedalboard, Compressor
        
        # Create a Pedalboard with a compressor
        board = Pedalboard([
            Compressor(
                threshold_db=threshold_db,
                ratio=ratio,
                attack_ms=attack_ms,
                release_ms=release_ms
            )
        ])
        
        # Apply the effects
        logger.info("Applying compression with Pedalboard")
        effected = board(audio_data, sample_rate)
        
        return effected
    except Exception as e:
        logger.error(f"Error applying compression with Pedalboard: {e}")
        
        # Fallback to pysndfx
        temp_in_path = None
        temp_out_path = None
        try:
            from pysndfx import AudioEffectsChain
            logger.info("Attempting compression with pysndfx")
            
            # Create temporary input file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_in_file:
                temp_in_path = temp_in_file.name
            sf.write(temp_in_path, audio_data, sample_rate)

            # Create temporary output file path (pysndfx will create the file)
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_out_file:
                temp_out_path = temp_out_file.name
            # If NamedTemporaryFile created a placeholder, remove it so pysndfx can create it.
            if os.path.exists(temp_out_path):
                os.remove(temp_out_path)

            # Create the effects chain
            fx = (
                AudioEffectsChain()
                .compand(attack_ms/1000, release_ms/1000, [threshold_db, -90, -70, -60, -50, -40, -30, -20, -10, 0], ratio)
            )
            
            fx(temp_in_path, temp_out_path)
            
            compressed_audio, _ = sf.read(temp_out_path)
            logger.info("Compression applied using pysndfx")
            
            return compressed_audio
            
        except Exception as alt_e:
            logger.error(f"Error applying compression with pysndfx: {alt_e}", exc_info=True)
            logger.warning("Compression failed, returning original audio")
            return audio_data
        finally:
            # Clean up temporary files
            for p in [temp_in_path, temp_out_path]:
                if p and os.path.exists(p):
                    try:
                        os.remove(p)
                        logger.debug(f"Cleaned up temp file: {p}")
                    except Exception as e_clean:
                        logger.error(f"Error cleaning up temp file {p}: {e_clean}", exc_info=True)

def is_stereo(audio_data):
    """Check if audio data is stereo (2 channels)"""
    return len(audio_data.shape) > 1 and audio_data.shape[0] == 2

def read_audio_file(file_path):
    """Read audio file using pedalboard's AudioFile which supports multiple formats"""
    try:
        with AudioFile(file_path) as f:
            audio_data = f.read(f.frames)
            sample_rate = f.samplerate
            
        logger.info(f"Successfully read audio file: {file_path}, SR: {sample_rate}, Shape: {audio_data.shape}")
        return audio_data, sample_rate
    except Exception as e:
        logger.error(f"Error reading audio file {file_path}: {e}")
        raise
