import numpy as np
import soundfile as sf
import pyloudnorm as pyln
import logging
from pedalboard.io import AudioFile
import tempfile
import os

logger = logging.getLogger(__name__)

def calculate_lufs(audio_data, sample_rate, window_size=1.0):
    """Calculate LUFS (Loudness Units Full Scale) for audio data"""
    try:
        # Create a meter
        meter = pyln.Meter(sample_rate)
        
        # Calculate LUFS
        if is_stereo(audio_data):
            # For stereo, use as is
            lufs = meter.integrated_loudness(audio_data.T)
        else:
            # For mono, reshape to match expected format
            lufs = meter.integrated_loudness(audio_data)
        
        # Calculate short-term LUFS for graphing
        hop_size = int(sample_rate * 0.1)  # 100ms hop size
        window_samples = int(sample_rate * window_size)
        
        times = []
        lufs_values = []
        
        for i in range(0, len(audio_data[0]) if is_stereo(audio_data) else len(audio_data), hop_size):
            if is_stereo(audio_data):
                chunk = audio_data[:, i:i+window_samples]
                if chunk.shape[1] < window_samples / 2:  # Skip small chunks
                    continue
                chunk_lufs = meter.integrated_loudness(chunk.T)
            else:
                chunk = audio_data[i:i+window_samples]
                if len(chunk) < window_samples / 2:  # Skip small chunks
                    continue
                chunk_lufs = meter.integrated_loudness(chunk)
            
            if not np.isnan(chunk_lufs) and not np.isinf(chunk_lufs):
                times.append(i / sample_rate)
                lufs_values.append(chunk_lufs)
        
        # Format data for Chart.js
        graph_data = {
            "times": times,
            "lufs": lufs_values
        }
        
        # Return just the LUFS value, not a tuple
        return lufs
    except Exception as e:
        logger.error(f"Error calculating LUFS: {e}")
        raise

def create_lufs_graph(audio_data, sample_rate):
    """
    Create data for a LUFS graph.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        
    Returns:
        dict: Graph data with time and LUFS values, or None on error
    """
    try:
        # Create a meter
        meter = pyln.Meter(sample_rate)
        
        # Calculate LUFS for each second
        segment_duration = 1.0  # 1 second segments
        segment_samples = int(segment_duration * sample_rate)
        
        times = []
        lufs_values = []
        
        # Handle mono vs stereo
        is_stereo_audio = is_stereo(audio_data)
        
        # Calculate LUFS for each segment
        for i in range(0, len(audio_data[0]) if is_stereo_audio else len(audio_data), segment_samples):
            if is_stereo_audio:
                segment = audio_data[:, i:i+segment_samples]
                if segment.shape[1] < segment_samples / 2:  # Skip small segments
                    continue
                segment_lufs = meter.integrated_loudness(segment.T)
            else:
                segment = audio_data[i:i+segment_samples]
                if len(segment) < segment_samples / 2:  # Skip small segments
                    continue
                segment_lufs = meter.integrated_loudness(segment)
            
            # Only add valid LUFS values
            if not np.isnan(segment_lufs) and not np.isinf(segment_lufs) and segment_lufs > -100:
                times.append(float(i / sample_rate))
                lufs_values.append(float(segment_lufs))
        
        # Ensure we have at least some data points
        if not times or not lufs_values:
            # If no valid segments, add a single point
            times = [0.0]
            lufs_values = [-70.0]
        
        return {
            'times': times,
            'lufs': lufs_values
        }
        
    except Exception as e:
        logger.error(f"Error creating LUFS graph: {e}", exc_info=True)
        return None

def normalize_audio_loudness(audio_data, sample_rate, target_lufs):
    """
    Normalize audio to a target LUFS level.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        target_lufs: Target LUFS level
        
    Returns:
        numpy.ndarray: Normalized audio data, or None on error
    """
    try:
        # Handle mono vs stereo
        is_mono = len(audio_data.shape) == 1 or audio_data.shape[1] == 1
        
        # Reshape mono audio for pyloudnorm
        if is_mono and len(audio_data.shape) == 1:
            data_for_meter = audio_data.reshape(-1, 1)
        else:
            data_for_meter = audio_data
        
        # Measure the current loudness
        meter = pyln.Meter(sample_rate)
        current_lufs = meter.integrated_loudness(data_for_meter)
        logger.info(f"Current LUFS: {current_lufs}, Target LUFS: {target_lufs}")
        
        # Handle very quiet audio (can cause assertion errors)
        if current_lufs < -70:
            logger.warning("Audio is too quiet for normalization, using default gain")
            # Apply a fixed gain instead
            gain_db = target_lufs + 70  # Rough estimate
            normalized_audio = audio_data * 10**(gain_db/20)
        else:
            # Normalize to target LUFS
            try:
                normalized_audio = pyln.normalize.loudness(audio_data, current_lufs, target_lufs)
            except AssertionError:
                logger.warning("Assertion error during normalization, using manual gain calculation")
                # Calculate gain manually
                gain_db = target_lufs - current_lufs
                normalized_audio = audio_data * 10**(gain_db/20)
        
        return normalized_audio
    except Exception as e:
        logger.error(f"Error normalizing audio: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def normalize_audio_with_true_peak_protection(audio_data, sample_rate, target_lufs, max_true_peak_db=-1.0):
    """
    Normalize audio to target LUFS while ensuring true peak never exceeds the limit.

    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        target_lufs: Target LUFS level
        max_true_peak_db: Maximum allowed true peak level in dBTP (default: -1.0)

    Returns:
        numpy.ndarray: Normalized audio data with true peak protection, or None on error
    """
    try:
        logger.info(f"Normalizing to {target_lufs} LUFS with true peak protection at {max_true_peak_db} dBTP")

        # First, do standard LUFS normalization
        normalized_audio = normalize_audio_loudness(audio_data, sample_rate, target_lufs)
        if normalized_audio is None:
            return None

        # Check if true peak exceeds limit
        true_peak = calculate_true_peak(normalized_audio, sample_rate)
        logger.info(f"True peak after LUFS normalization: {true_peak:.2f} dBTP")

        if true_peak <= max_true_peak_db:
            logger.info("True peak within limits after LUFS normalization")
            return normalized_audio

        # If true peak exceeds limit, we need to reduce the gain
        excess_db = true_peak - max_true_peak_db + 0.1  # 0.1 dB safety margin
        reduction_linear = 10 ** (-excess_db / 20)

        logger.info(f"Reducing gain by {excess_db:.2f} dB to maintain true peak limit")
        protected_audio = normalized_audio * reduction_linear

        # Verify the result
        final_true_peak = calculate_true_peak(protected_audio, sample_rate)
        final_lufs = calculate_lufs(protected_audio, sample_rate)

        logger.info(f"Final result: LUFS={final_lufs:.2f}, True Peak={final_true_peak:.2f} dBTP")

        return protected_audio

    except Exception as e:
        logger.error(f"Error in true peak protected normalization: {e}")
        return None

def apply_peak_limiter(audio_data, sample_rate, peak_reduction_db=5.0):
    """
    Apply aggressive lookahead peak limiting to catch ALL peaks, including very short transients.
    Uses lookahead buffering to ensure no peak escapes.

    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        peak_reduction_db: How much to reduce peaks by (default: 5.0 dB)

    Returns:
        numpy.ndarray: Peak-limited audio data with lookahead processing
    """
    try:
        logger.info(f"Applying lookahead peak limiter with -{peak_reduction_db} dB peak reduction")

        # Lookahead parameters
        lookahead_ms = 5.0  # 5ms lookahead to catch all transients
        lookahead_samples = int(lookahead_ms * sample_rate / 1000)

        # Handle stereo vs mono
        if is_stereo(audio_data):
            channels, samples = audio_data.shape
            output_audio = np.zeros_like(audio_data)

            for ch in range(channels):
                output_audio[ch] = _apply_lookahead_limiting_mono(
                    audio_data[ch], sample_rate, peak_reduction_db, lookahead_samples
                )
        else:
            output_audio = _apply_lookahead_limiting_mono(
                audio_data, sample_rate, peak_reduction_db, lookahead_samples
            )

        # Measure the peak reduction achieved
        original_peak = np.max(np.abs(audio_data))
        limited_peak = np.max(np.abs(output_audio))

        if original_peak > 0 and limited_peak > 0:
            actual_reduction = 20 * np.log10(original_peak / limited_peak)
            logger.info(f"Lookahead peak limiter achieved {actual_reduction:.2f} dB peak reduction")
        else:
            logger.info(f"Lookahead peak limiter applied {peak_reduction_db:.2f} dB reduction")

        return output_audio

    except Exception as e:
        logger.error(f"Error applying lookahead peak limiter: {e}")
        return audio_data  # Return original if limiting fails

def _apply_lookahead_limiting_mono(audio_mono, sample_rate, peak_reduction_db, lookahead_samples):
    """
    Apply lookahead limiting to a mono audio channel.
    This is a true brick-wall limiter that catches every single peak.
    """
    try:
        # Target peak level after reduction
        target_peak_linear = 10 ** (-peak_reduction_db / 20)

        # Pad audio with zeros for lookahead processing
        padded_audio = np.concatenate([audio_mono, np.zeros(lookahead_samples)])
        output_audio = np.zeros_like(padded_audio)

        # Envelope follower parameters
        attack_coeff = np.exp(-1.0 / (0.1 * sample_rate / 1000))  # 0.1ms attack
        release_coeff = np.exp(-1.0 / (5.0 * sample_rate / 1000))  # 5ms release

        envelope = 0.0

        # Process each sample with lookahead
        for i in range(len(audio_mono)):
            # Look ahead to find the maximum peak in the lookahead window
            lookahead_end = min(i + lookahead_samples, len(padded_audio))
            lookahead_peak = np.max(np.abs(padded_audio[i:lookahead_end]))

            # Calculate required gain reduction
            if lookahead_peak > target_peak_linear:
                target_gain = target_peak_linear / lookahead_peak
            else:
                target_gain = 1.0

            # Smooth gain changes with envelope follower
            if target_gain < envelope:
                # Attack (gain reduction)
                envelope = target_gain + (envelope - target_gain) * attack_coeff
            else:
                # Release (gain recovery)
                envelope = target_gain + (envelope - target_gain) * release_coeff

            # Apply gain reduction
            output_audio[i] = padded_audio[i] * envelope

        # Remove the padding
        return output_audio[:len(audio_mono)]

    except Exception as e:
        logger.error(f"Error in mono lookahead limiting: {e}")
        return audio_mono

def apply_compression(audio_data, sample_rate, threshold_db=-20, ratio=4, attack_ms=5, release_ms=100):
    """
    Applies compression to audio data.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        threshold_db: Threshold in dB where compression begins
        ratio: Compression ratio
        attack_ms: Attack time in milliseconds
        release_ms: Release time in milliseconds
        
    Returns:
        numpy.ndarray: Compressed audio data, or None on error
    """
    logger.info(f"Starting compression with threshold={threshold_db}, ratio={ratio}, attack={attack_ms}, release={release_ms}")
    
    try:
        from pedalboard import Pedalboard, Compressor
        
        # Create a Pedalboard with a compressor
        board = Pedalboard([
            Compressor(
                threshold_db=threshold_db,
                ratio=ratio,
                attack_ms=attack_ms,
                release_ms=release_ms
            )
        ])
        
        # Apply the effects
        logger.info("Applying compression with Pedalboard")
        effected = board(audio_data, sample_rate)
        
        return effected
    except Exception as e:
        logger.error(f"Error applying compression with Pedalboard: {e}")
        
        # Fallback to pysndfx
        temp_in_path = None
        temp_out_path = None
        try:
            from pysndfx import AudioEffectsChain
            logger.info("Attempting compression with pysndfx")
            
            # Create temporary input file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_in_file:
                temp_in_path = temp_in_file.name
            sf.write(temp_in_path, audio_data, sample_rate)

            # Create temporary output file path (pysndfx will create the file)
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_out_file:
                temp_out_path = temp_out_file.name
            # If NamedTemporaryFile created a placeholder, remove it so pysndfx can create it.
            if os.path.exists(temp_out_path):
                os.remove(temp_out_path)

            # Create the effects chain
            fx = (
                AudioEffectsChain()
                .compand(attack_ms/1000, release_ms/1000, [threshold_db, -90, -70, -60, -50, -40, -30, -20, -10, 0], ratio)
            )
            
            fx(temp_in_path, temp_out_path)
            
            compressed_audio, _ = sf.read(temp_out_path)
            logger.info("Compression applied using pysndfx")
            
            return compressed_audio
            
        except Exception as alt_e:
            logger.error(f"Error applying compression with pysndfx: {alt_e}", exc_info=True)
            logger.warning("Compression failed, returning original audio")
            return audio_data
        finally:
            # Clean up temporary files
            for p in [temp_in_path, temp_out_path]:
                if p and os.path.exists(p):
                    try:
                        os.remove(p)
                        logger.debug(f"Cleaned up temp file: {p}")
                    except Exception as e_clean:
                        logger.error(f"Error cleaning up temp file {p}: {e_clean}", exc_info=True)

def calculate_true_peak(audio_data, sample_rate):
    """
    Calculate true peak level in dBTP (decibels True Peak).

    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio

    Returns:
        float: True peak level in dBTP
    """
    try:
        # Upsample by 4x for true peak detection (ITU-R BS.1770-4 standard)
        from scipy import signal

        # Handle mono vs stereo
        if is_stereo(audio_data):
            # For stereo, check both channels
            max_peak = 0
            for channel in range(audio_data.shape[0]):
                # Upsample the channel
                upsampled = signal.resample(audio_data[channel], len(audio_data[channel]) * 4)
                # Find absolute maximum
                channel_peak = np.max(np.abs(upsampled))
                max_peak = max(max_peak, channel_peak)
        else:
            # For mono
            upsampled = signal.resample(audio_data, len(audio_data) * 4)
            max_peak = np.max(np.abs(upsampled))

        # Convert to dBTP
        if max_peak > 0:
            true_peak_db = 20 * np.log10(max_peak)
        else:
            true_peak_db = -np.inf

        return true_peak_db
    except Exception as e:
        logger.error(f"Error calculating true peak: {e}")
        return 0.0  # Conservative fallback

def apply_true_peak_limiter(audio_data, sample_rate, max_true_peak_db=-1.0):
    """
    Apply true peak limiting to ensure audio never exceeds the specified true peak level.
    Uses intelligent limiting that preserves audio quality while ensuring compliance.

    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        max_true_peak_db: Maximum allowed true peak level in dBTP (default: -1.0)

    Returns:
        numpy.ndarray: Limited audio data
    """
    try:
        logger.info(f"Applying true peak limiter with max level: {max_true_peak_db} dBTP")

        # Calculate current true peak
        current_true_peak = calculate_true_peak(audio_data, sample_rate)
        logger.info(f"Current true peak: {current_true_peak:.2f} dBTP")

        if current_true_peak <= max_true_peak_db:
            logger.info(f"Audio already within true peak limit ({current_true_peak:.2f} dBTP)")
            return audio_data

        # First, try using Pedalboard's Limiter for transparent limiting
        try:
            from pedalboard import Pedalboard, Limiter

            # Use a slightly more conservative threshold to ensure we stay under the limit
            limiter_threshold = max_true_peak_db - 0.1  # 0.1 dB safety margin

            # Create a limiter with fast attack and moderate release
            board = Pedalboard([
                Limiter(
                    threshold_db=limiter_threshold,
                    release_ms=5.0  # Fast but not too aggressive release
                )
            ])

            # Apply the limiter
            limited_audio = board(audio_data, sample_rate)

            # Verify the true peak level
            final_true_peak = calculate_true_peak(limited_audio, sample_rate)
            logger.info(f"True peak after Pedalboard limiting: {final_true_peak:.2f} dBTP")

            # If still over limit, apply manual reduction
            if final_true_peak > max_true_peak_db:
                logger.warning(f"Pedalboard limiter insufficient, applying manual reduction")
                reduction_db = final_true_peak - max_true_peak_db + 0.1  # Extra 0.1 dB safety
                reduction_linear = 10 ** (-reduction_db / 20)
                limited_audio = limited_audio * reduction_linear

                final_true_peak = calculate_true_peak(limited_audio, sample_rate)
                logger.info(f"True peak after manual correction: {final_true_peak:.2f} dBTP")

            return limited_audio

        except ImportError:
            logger.warning("Pedalboard not available, using manual true peak limiting")

            # Manual true peak limiting with safety margin
            reduction_db = current_true_peak - max_true_peak_db + 0.1  # 0.1 dB safety margin
            reduction_linear = 10 ** (-reduction_db / 20)

            logger.info(f"Reducing audio by {reduction_db:.2f} dB to meet true peak limit")

            # Apply reduction
            limited_audio = audio_data * reduction_linear

            # Verify the result
            final_true_peak = calculate_true_peak(limited_audio, sample_rate)
            logger.info(f"True peak after manual limiting: {final_true_peak:.2f} dBTP")

            return limited_audio

    except Exception as e:
        logger.error(f"Error applying true peak limiter: {e}")
        # Conservative fallback: reduce by 3dB to be safe
        logger.warning("Applying conservative 3dB reduction as fallback")
        return audio_data * 0.707  # -3dB reduction

def is_stereo(audio_data):
    """Check if audio data is stereo (2 channels)"""
    return len(audio_data.shape) > 1 and audio_data.shape[0] == 2

def read_audio_file(file_path):
    """Read audio file using pedalboard's AudioFile which supports multiple formats"""
    try:
        with AudioFile(file_path) as f:
            audio_data = f.read(f.frames)
            sample_rate = f.samplerate
            
        logger.info(f"Successfully read audio file: {file_path}, SR: {sample_rate}, Shape: {audio_data.shape}")
        return audio_data, sample_rate
    except Exception as e:
        logger.error(f"Error reading audio file {file_path}: {e}")
        raise
