<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LUFS Analyzer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <!-- Add Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>LUFS Audio Analyzer</h1>
        
        <form id="audio-form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select Audio File:</label>
                <input type="file" id="file" name="file" accept=".wav,.mp3,.ogg,.flac,.aiff,.m4a" required>
                <small class="form-text text-muted">Supported formats: WAV, MP3, OGG, FLAC, AIFF, M4A</small>
            </div>
            
            <div class="form-group">
                <label for="target_lufs">Target LUFS (dB):</label>
                <input type="number" id="target_lufs" name="max_lufs" value="-23" step="0.1">
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="compression" name="compression">
                    Apply Compression
                </label>
            </div>
            
            <div id="compression-options" style="display: none;">
                <div class="compression-controls">
                    <h4 class="compression-title">Compression Settings</h4>
                    
                    <div class="form-group">
                        <label for="threshold">Threshold (dB):</label>
                        <div class="slider-wrapper">
                            <input type="range" id="threshold" name="threshold" min="-60" max="0" value="-5" step="1" class="form-control-range">
                            <span id="threshold-value-tooltip" class="slider-value-tooltip">-5 dB</span>
                        </div>
                        <div class="slider-label">
                            <span>-60 dB</span>
                            <span>0 dB</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="ratio">Ratio:</label>
                        <div class="slider-wrapper">
                            <input type="range" id="ratio" name="ratio" min="1" max="20" value="4" step="0.5" class="form-control-range">
                            <span id="ratio-value-tooltip" class="slider-value-tooltip">4:1</span>
                        </div>
                        <div class="slider-label">
                            <span>1:1</span>
                            <span>20:1</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="attack">Attack (ms):</label>
                        <div class="slider-wrapper">
                            <input type="range" id="attack" name="attack" min="0.1" max="100" value="20" step="0.1" class="form-control-range">
                            <span id="attack-value-tooltip" class="slider-value-tooltip">20 ms</span>
                        </div>
                        <div class="slider-label">
                            <span>0.1 ms</span>
                            <span>100 ms</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="release">Release (ms):</label>
                        <div class="slider-wrapper">
                            <input type="range" id="release" name="release" min="10" max="1000" value="100" step="10" class="form-control-range">
                            <span id="release-value-tooltip" class="slider-value-tooltip">100 ms</span>
                        </div>
                        <div class="slider-label">
                            <span>10 ms</span>
                            <span>1000 ms</span>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button type="button" id="compression-info-btn" class="info-button">
                            What is compression?
                        </button>
                        <div id="compression-info" class="info-box hidden">
                            <p><strong>Audio compression</strong> reduces the dynamic range of audio by attenuating loud parts while leaving quieter parts unchanged.</p>
                            <ul>
                                <li><strong>Threshold:</strong> The level at which compression begins. Signals above this level will be compressed.</li>
                                <li><strong>Ratio:</strong> Determines how much compression is applied. A 4:1 ratio means that for every 4dB above the threshold, the output will only increase by 1dB.</li>
                                <li><strong>Attack:</strong> How quickly compression is applied when the signal exceeds the threshold.</li>
                                <li><strong>Release:</strong> How quickly compression is removed when the signal falls below the threshold.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label>
                    <input type="checkbox" id="nrk-podcast-check" name="nrk_podcast_check">
                    Detect NRK Podcast Jingle
                </label>
                <small class="form-text text-muted d-block">Automatically find NRK podcast jingle/intro in your audio file</small>
            </div>

            <div class="form-group">
                <button type="submit" id="upload-button" class="btn btn-primary">
                    <span id="button-text">Analyze Audio</span>
                    <span id="spinner" class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                </button>
            </div>
        </form>
        
        <!-- Progress container -->
        <div id="progress-container" style="display: none; margin-top: 20px;">
            <h3>Processing Audio</h3>
            <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
                <div id="progress-bar" class="progress-bar" role="progressbar" 
                     style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <p id="status-message">Uploading file...</p>
        </div>
        
        <!-- Results section -->
        <div id="results" class="mt-4" style="display: none;">
            <h3>Analysis Results</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header">LUFS Values</div>
                        <div class="card-body">
                            <p>Original Audio: <span id="lufs-value" class="font-weight-bold">N/A</span></p>
                            <div id="processed-lufs-container">
                                <p>Processed Audio: <span id="processed-lufs-value" class="font-weight-bold">N/A</span></p>
                            </div>
                            <a id="download-link" href="#" class="btn btn-primary mt-2" style="display: none;">Download Processed Audio</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">LUFS Comparison</div>
                <div class="card-body">
                    <div class="graph-container">
                        <canvas id="comparison-graph"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jingle results section -->
        <div id="jingle-results" class="mt-4" style="display: none;">
            <h3>NRK Podcast Jingle Detection</h3>
            <p>The following occurrences of the NRK podcast jingle were found:</p>
            <ul id="jingle-matches-list" class="list-group">
                <!-- Matches will be inserted here -->
            </ul>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        // Show/hide compression options
        document.getElementById('compression').addEventListener('change', function() {
            const compressionOptions = document.getElementById('compression-options');
            compressionOptions.style.display = this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>

<div id="progress-container" style="display: none; margin-top: 20px;">
    <h3>Processing Audio</h3>
    <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
        <div id="progress-bar" class="progress-bar" role="progressbar" 
             style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
    </div>
    <p id="status-message">Uploading file...</p>
</div>
