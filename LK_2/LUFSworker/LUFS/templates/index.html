<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LUFS Analyzer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <!-- Add Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>LUFS Audio Analyzer</h1>
        
        <form id="audio-form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select Audio File:</label>
                <input type="file" id="file" name="file" accept=".wav,.mp3,.ogg,.flac,.aiff,.m4a" required>
                <small class="form-text text-muted">Supported formats: WAV, MP3, OGG, FLAC, AIFF, M4A</small>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="target_lufs">Target LUFS</label>
                        <div class="knob-container">
                            <div class="knob-wrapper">
                                <div class="knob" id="lufs-knob">
                                    <div class="knob-indicator"></div>
                                    <div class="knob-value" id="knob-value">-23</div>
                                    <div class="knob-unit">LUFS</div>
                                </div>
                                <input type="range" id="target_lufs" name="max_lufs" min="-30" max="-10" value="-23" step="0.5" class="knob-input" style="display: none;">
                            </div>
                            <div class="knob-labels">
                                <span class="knob-label-left">-30<br><small>Quiet</small></span>
                                <span class="knob-label-right">-10<br><small>Loud</small></span>
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            Final loudness target
                        </small>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label for="threshold">Compression Threshold</label>
                        <div class="knob-container">
                            <div class="knob-wrapper">
                                <div class="knob" id="threshold-knob">
                                    <div class="knob-indicator"></div>
                                    <div class="knob-value" id="threshold-knob-value">-12</div>
                                    <div class="knob-unit">dB</div>
                                </div>
                                <input type="range" id="threshold" name="threshold" min="-20" max="-3" value="-12" step="0.5" class="knob-input" style="display: none;">
                            </div>
                            <div class="knob-labels">
                                <span class="knob-label-left">-20<br><small>Light</small></span>
                                <span class="knob-label-right">-3<br><small>Heavy</small></span>
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            Lower = more compression = higher volume
                        </small>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="compression" name="compression" checked>
                    Apply Podcast Compression
                </label>
                <small class="form-text text-muted">
                    50ms attack, 100ms release, 2.8:1 ratio - optimized for speech content and maximum volume
                </small>
            </div>

            <!-- Hidden inputs for automatic compression settings -->
            <input type="hidden" id="threshold" name="threshold" value="-5">
            <input type="hidden" id="ratio" name="ratio" value="4">
            <input type="hidden" id="attack" name="attack" value="20">
            <input type="hidden" id="release" name="release" value="100">

            <div id="compression-info-section" class="form-group">
                <button type="button" id="compression-info-btn" class="info-button">
                    How does smart processing work?
                </button>
                <div id="compression-info" class="info-box hidden">
                    <p><strong>Podcast-Optimized Compression</strong> with manual threshold control:</p>
                    <ul>
                        <li><strong>Attack Time:</strong> Fixed at 50ms for natural speech characteristics</li>
                        <li><strong>Release Time:</strong> Fixed at 100ms for smooth, transparent compression</li>
                        <li><strong>Ratio:</strong> Fixed at 2.8:1 for gentle but effective dynamic control</li>
                        <li><strong>Threshold:</strong> Manual control from -20dB to -3dB</li>
                    </ul>
                    <p><strong>Threshold Control:</strong></p>
                    <ul>
                        <li><strong>-20dB to -15dB:</strong> Light compression - preserves dynamics</li>
                        <li><strong>-14dB to -10dB:</strong> Moderate compression - balanced control</li>
                        <li><strong>-9dB to -6dB:</strong> Strong compression - higher volume</li>
                        <li><strong>-5dB to -3dB:</strong> Heavy compression - maximum volume</li>
                    </ul>
                    <p><strong>How it works:</strong> Lower threshold = compression starts earlier = more compression = higher overall volume. Adjust the threshold knob to control how much compression is applied while keeping the other settings optimized for speech.</p>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label>
                    <input type="checkbox" id="nrk-podcast-check" name="nrk_podcast_check">
                    Detect NRK Podcast Jingle
                </label>
                <small class="form-text text-muted d-block">Automatically find NRK podcast jingle/intro in your audio file</small>
            </div>

            <div class="form-group">
                <button type="submit" id="upload-button" class="btn btn-primary">
                    <span id="button-text">Analyze Audio</span>
                    <span id="spinner" class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                </button>
            </div>
        </form>
        
        <!-- Progress container -->
        <div id="progress-container" style="display: none; margin-top: 20px;">
            <h3>Processing Audio</h3>
            <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
                <div id="progress-bar" class="progress-bar" role="progressbar" 
                     style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <p id="status-message">Uploading file...</p>
        </div>
        
        <!-- Results section -->
        <div id="results" class="mt-4" style="display: none;">
            <h3>Analysis Results</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header">Audio Measurements</div>
                        <div class="card-body">
                            <h6>Original Audio</h6>
                            <p>LUFS: <span id="lufs-value" class="font-weight-bold">N/A</span></p>
                            <p>True Peak: <span id="true-peak-value" class="font-weight-bold">N/A</span></p>

                            <div id="processed-lufs-container" style="display: none;">
                                <h6 class="mt-3">Processed Audio</h6>
                                <p>LUFS: <span id="processed-lufs-value" class="font-weight-bold">N/A</span></p>
                                <p>True Peak: <span id="processed-true-peak-value" class="font-weight-bold">N/A</span></p>
                            </div>

                            <a id="download-link" href="#" class="btn btn-primary mt-2" style="display: none;">Download Processed Audio</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">LUFS Comparison</div>
                <div class="card-body">
                    <div class="graph-container">
                        <canvas id="comparison-graph"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jingle results section -->
        <div id="jingle-results" class="mt-4" style="display: none;">
            <h3>NRK Podcast Jingle Detection</h3>
            <p>The following occurrences of the NRK podcast jingle were found:</p>
            <ul id="jingle-matches-list" class="list-group">
                <!-- Matches will be inserted here -->
            </ul>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        // Show/hide compression options
        document.getElementById('compression').addEventListener('change', function() {
            const compressionOptions = document.getElementById('compression-options');
            compressionOptions.style.display = this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>

<div id="progress-container" style="display: none; margin-top: 20px;">
    <h3>Processing Audio</h3>
    <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
        <div id="progress-bar" class="progress-bar" role="progressbar" 
             style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
    </div>
    <p id="status-message">Uploading file...</p>
</div>
