<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LUFS Analyzer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <!-- Add Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>LUFS Audio Analyzer</h1>
        
        <form id="audio-form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select Audio File:</label>
                <input type="file" id="file" name="file" accept=".wav,.mp3,.ogg,.flac,.aiff,.m4a" required>
                <small class="form-text text-muted">Supported formats: WAV, MP3, OGG, FLAC, AIFF, M4A</small>
            </div>
            
            <div class="form-group">
                <label for="target_lufs">Target LUFS (dB):</label>
                <div class="slider-wrapper">
                    <input type="range" id="target_lufs" name="max_lufs" min="-30" max="-10" value="-23" step="0.5" class="form-control-range">
                    <span id="target-lufs-value-tooltip" class="slider-value-tooltip">-23 LUFS</span>
                </div>
                <div class="slider-label">
                    <span>-30 LUFS (Quiet)</span>
                    <span>-10 LUFS (Loud)</span>
                </div>
                <small class="form-text text-muted">
                    Automatically adjusts compression settings based on target loudness
                </small>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="compression" name="compression" checked>
                    Apply Smart Dynamic Processing
                </label>
                <small class="form-text text-muted">
                    Automatically optimizes compression, limiting, and normalization
                </small>
            </div>

            <!-- Hidden inputs for automatic compression settings -->
            <input type="hidden" id="threshold" name="threshold" value="-5">
            <input type="hidden" id="ratio" name="ratio" value="4">
            <input type="hidden" id="attack" name="attack" value="20">
            <input type="hidden" id="release" name="release" value="100">

            <div id="compression-info-section" class="form-group">
                <button type="button" id="compression-info-btn" class="info-button">
                    How does smart processing work?
                </button>
                <div id="compression-info" class="info-box hidden">
                    <p><strong>Smart Dynamic Processing</strong> automatically adjusts multiple audio parameters based on your target LUFS:</p>
                    <ul>
                        <li><strong>Louder targets (-10 to -16 LUFS):</strong> Gentle compression with fast attack for broadcast/streaming</li>
                        <li><strong>Standard targets (-17 to -23 LUFS):</strong> Balanced compression for podcasts and general content</li>
                        <li><strong>Quieter targets (-24 to -30 LUFS):</strong> Minimal compression, preserving dynamics</li>
                    </ul>
                    <p>The system automatically sets optimal threshold, ratio, attack, and release times.</p>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label>
                    <input type="checkbox" id="nrk-podcast-check" name="nrk_podcast_check">
                    Detect NRK Podcast Jingle
                </label>
                <small class="form-text text-muted d-block">Automatically find NRK podcast jingle/intro in your audio file</small>
            </div>

            <div class="form-group">
                <button type="submit" id="upload-button" class="btn btn-primary">
                    <span id="button-text">Analyze Audio</span>
                    <span id="spinner" class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                </button>
            </div>
        </form>
        
        <!-- Progress container -->
        <div id="progress-container" style="display: none; margin-top: 20px;">
            <h3>Processing Audio</h3>
            <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
                <div id="progress-bar" class="progress-bar" role="progressbar" 
                     style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <p id="status-message">Uploading file...</p>
        </div>
        
        <!-- Results section -->
        <div id="results" class="mt-4" style="display: none;">
            <h3>Analysis Results</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header">LUFS Values</div>
                        <div class="card-body">
                            <p>Original Audio: <span id="lufs-value" class="font-weight-bold">N/A</span></p>
                            <div id="processed-lufs-container">
                                <p>Processed Audio: <span id="processed-lufs-value" class="font-weight-bold">N/A</span></p>
                            </div>
                            <a id="download-link" href="#" class="btn btn-primary mt-2" style="display: none;">Download Processed Audio</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">LUFS Comparison</div>
                <div class="card-body">
                    <div class="graph-container">
                        <canvas id="comparison-graph"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jingle results section -->
        <div id="jingle-results" class="mt-4" style="display: none;">
            <h3>NRK Podcast Jingle Detection</h3>
            <p>The following occurrences of the NRK podcast jingle were found:</p>
            <ul id="jingle-matches-list" class="list-group">
                <!-- Matches will be inserted here -->
            </ul>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        // Show/hide compression options
        document.getElementById('compression').addEventListener('change', function() {
            const compressionOptions = document.getElementById('compression-options');
            compressionOptions.style.display = this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>

<div id="progress-container" style="display: none; margin-top: 20px;">
    <h3>Processing Audio</h3>
    <div class="progress" style="height: 25px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 10px;">
        <div id="progress-bar" class="progress-bar" role="progressbar" 
             style="width: 0%; height: 100%; background-color: #4CAF50; color: white; text-align: center; line-height: 25px; border-radius: 4px;" 
             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
    </div>
    <p id="status-message">Uploading file...</p>
</div>
