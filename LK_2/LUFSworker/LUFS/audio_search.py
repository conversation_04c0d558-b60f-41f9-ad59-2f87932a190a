import os
import numpy as np
import librosa
import soundfile as sf
from scipy.signal import correlate
import logging

# Set up logging
logger = logging.getLogger(__name__)
jingle_path = 'jingles/En podkast - Mann.wav'

def extract_spectral_features(audio, sample_rate):
    """Extract spectral features for audio matching"""
    # Extract MFCC features (Mel-frequency cepstral coefficients)
    mfccs = librosa.feature.mfcc(y=audio, sr=sample_rate, n_mfcc=13)

    # Extract spectral centroid
    spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sample_rate)

    # Extract chroma features
    chroma = librosa.feature.chroma_stft(y=audio, sr=sample_rate)

    # Extract zero crossing rate
    zcr = librosa.feature.zero_crossing_rate(audio)

    return {
        'mfcc': mfccs,
        'spectral_centroid': spectral_centroids,
        'chroma': chroma,
        'zcr': zcr
    }

def sliding_window_spectral_match(audio, sample_rate, jingle_audio, jingle_features, threshold):
    """Perform sliding window spectral matching"""
    jingle_length = len(jingle_audio)
    hop_length = sample_rate // 4  # 0.25 second hops
    matches = []

    logger.info(f"Sliding window analysis: jingle_length={jingle_length}, hop_length={hop_length}")

    # Slide window across the audio
    for start_sample in range(0, len(audio) - jingle_length + 1, hop_length):
        end_sample = start_sample + jingle_length
        window_audio = audio[start_sample:end_sample]

        # Extract features for this window
        window_features = extract_spectral_features(window_audio, sample_rate)

        # Calculate similarity
        similarity = calculate_spectral_similarity(window_features, jingle_features)

        if similarity > threshold:
            start_time = start_sample / sample_rate
            end_time = end_sample / sample_rate

            matches.append({
                "start": float(start_time),
                "end": float(end_time),
                "confidence": float(similarity)
            })

            logger.info(f"Found potential match at {start_time:.2f}s with confidence {similarity:.3f}")

    return matches

def calculate_spectral_similarity(features1, features2):
    """Calculate similarity between two sets of spectral features"""
    debug_info = {}

    # Calculate weighted similarity scores
    weighted_scores = []
    weights = []

    # MFCC similarity (most important for audio matching)
    mfcc_corr = np.corrcoef(features1['mfcc'].flatten(), features2['mfcc'].flatten())[0, 1]
    debug_info['mfcc_corr'] = mfcc_corr
    if not np.isnan(mfcc_corr):
        weighted_scores.append(mfcc_corr)
        weights.append(0.6)

    # Spectral centroid similarity
    centroid_corr = np.corrcoef(features1['spectral_centroid'].flatten(), features2['spectral_centroid'].flatten())[0, 1]
    debug_info['centroid_corr'] = centroid_corr
    if not np.isnan(centroid_corr):
        weighted_scores.append(centroid_corr)
        weights.append(0.2)

    # Chroma similarity
    chroma_corr = np.corrcoef(features1['chroma'].flatten(), features2['chroma'].flatten())[0, 1]
    debug_info['chroma_corr'] = chroma_corr
    if not np.isnan(chroma_corr):
        weighted_scores.append(chroma_corr)
        weights.append(0.15)

    # ZCR similarity
    zcr_corr = np.corrcoef(features1['zcr'].flatten(), features2['zcr'].flatten())[0, 1]
    debug_info['zcr_corr'] = zcr_corr
    if not np.isnan(zcr_corr):
        weighted_scores.append(zcr_corr)
        weights.append(0.05)

    # Calculate weighted average
    if weighted_scores and weights:
        weighted_sum = sum(score * weight for score, weight in zip(weighted_scores, weights))
        total_weight = sum(weights)
        final_similarity = max(0.0, min(1.0, float(weighted_sum / total_weight)))

        # Optional debug logging (disabled for production)
        # logger.debug(f"Similarity: MFCC={debug_info['mfcc_corr']:.3f}, Final={final_similarity:.3f}")

        return final_similarity
    else:
        return 0.0

def remove_overlapping_matches(matches):
    """Remove overlapping matches, keeping the one with highest confidence"""
    if not matches:
        return []

    # Sort by confidence (highest first)
    sorted_matches = sorted(matches, key=lambda x: x['confidence'], reverse=True)
    filtered_matches = []

    for match in sorted_matches:
        # Check if this match overlaps with any already accepted match
        overlaps = False
        for accepted_match in filtered_matches:
            if (match['start'] < accepted_match['end'] and match['end'] > accepted_match['start']):
                overlaps = True
                break

        if not overlaps:
            filtered_matches.append(match)

    return filtered_matches

def find_jingle_occurrences(audio_data, sample_rate, jingle_path, threshold=0.85):
    """
    Find occurrences of a jingle/intro in audio data.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        jingle_path: Path to the jingle/intro audio file
        threshold: Correlation threshold for matches (0.5-0.95)
        
    Returns:
        list: List of dictionaries with match information
    """
    try:
        logger.info(f"Starting jingle detection with threshold={threshold}")

        # Convert main audio to mono if stereo
        if len(audio_data.shape) > 1:
            logger.info(f"Converting stereo audio to mono for correlation, original shape: {audio_data.shape}")
            # Handle different stereo formats: (channels, samples) or (samples, channels)
            if audio_data.shape[0] == 2 and audio_data.shape[1] > audio_data.shape[0]:
                # Format: (2, samples) - channels first
                audio_mono = np.mean(audio_data, axis=0)
            elif audio_data.shape[1] == 2 and audio_data.shape[0] > audio_data.shape[1]:
                # Format: (samples, 2) - samples first
                audio_mono = np.mean(audio_data, axis=1)
            else:
                # Fallback: assume first dimension is samples
                audio_mono = np.mean(audio_data, axis=-1)
            logger.info(f"Converted to mono, new shape: {audio_mono.shape}")
        else:
            audio_mono = audio_data

        # Load jingle file (librosa loads as mono by default)
        y_jingle, sr_jingle = librosa.load(jingle_path, sr=None, mono=True)

        # Resample jingle if needed
        if sr_jingle != sample_rate:
            logger.info(f"Resampling jingle from {sr_jingle}Hz to {sample_rate}Hz")
            y_jingle = librosa.resample(y_jingle, orig_sr=sr_jingle, target_sr=sample_rate)
        
        # Get audio durations
        main_duration = len(audio_mono) / sample_rate
        jingle_duration = len(y_jingle) / sample_rate

        logger.info(f"Audio durations: main={main_duration:.2f}s, jingle={jingle_duration:.2f}s")

        # Use spectral features for more robust matching
        logger.info("Extracting spectral features for jingle")
        jingle_features = extract_spectral_features(y_jingle, sample_rate)

        # Perform sliding window analysis
        logger.info("Performing sliding window spectral analysis")
        matches = sliding_window_spectral_match(audio_mono, sample_rate, y_jingle, jingle_features, threshold)

        # Remove overlapping matches, keeping the one with highest confidence
        filtered_matches = remove_overlapping_matches(matches)

        logger.info(f"Found {len(filtered_matches)} potential matches after filtering")

        # Sort by confidence and limit to top 5
        filtered_matches.sort(key=lambda x: x['confidence'], reverse=True)
        filtered_matches = filtered_matches[:5]

        return filtered_matches
        
    except Exception as e:
        logger.error(f"Error in jingle detection: {e}", exc_info=True)
        return []