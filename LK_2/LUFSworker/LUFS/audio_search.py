import os
import numpy as np
import librosa
import soundfile as sf
from scipy.signal import correlate
import logging

# Set up logging
logger = logging.getLogger(__name__)
jingle_path = 'jingles/En podkast - Mann.wav'

def find_jingle_occurrences(audio_data, sample_rate, jingle_path, threshold=0.7):
    """
    Find occurrences of a jingle/intro in audio data.
    
    Args:
        audio_data: NumPy array of audio data
        sample_rate: Sample rate of the audio
        jingle_path: Path to the jingle/intro audio file
        threshold: Correlation threshold for matches (0.5-0.95)
        
    Returns:
        list: List of dictionaries with match information
    """
    try:
        logger.info(f"Starting jingle detection with threshold={threshold}")
        
        # Load jingle file
        y_jingle, sr_jingle = librosa.load(jingle_path, sr=None)
        
        # Resample jingle if needed
        if sr_jingle != sample_rate:
            logger.info(f"Resampling jingle from {sr_jingle}Hz to {sample_rate}Hz")
            y_jingle = librosa.resample(y_jingle, orig_sr=sr_jingle, target_sr=sample_rate)
        
        # Get audio durations
        main_duration = len(audio_data) / sample_rate
        jingle_duration = len(y_jingle) / sample_rate
        
        logger.info(f"Audio durations: main={main_duration:.2f}s, jingle={jingle_duration:.2f}s")
        
        # Normalize audio for more stable correlation
        y_main_norm = (audio_data - np.mean(audio_data)) / (np.std(audio_data) + 1e-10)
        y_jingle_norm = (y_jingle - np.mean(y_jingle)) / (np.std(y_jingle) + 1e-10)
        
        # Perform cross-correlation
        logger.info("Performing cross-correlation")
        correlation = correlate(y_main_norm, y_jingle_norm, mode='valid', method='fft')
        
        # Normalize correlation to be between 0 and 1
        correlation_normalized = (correlation - np.min(correlation)) / (np.max(correlation) - np.min(correlation) + 1e-10)
        
        # Find peaks in correlation that exceed threshold
        occurrences = []
        jingle_duration_samples = len(y_jingle)
        
        # Simple peak detection
        logger.info(f"Finding peaks with threshold {threshold}")
        for i in range(len(correlation_normalized)):
            if correlation_normalized[i] > threshold:
                # Check if this is a local maximum
                is_peak = True
                window = min(1000, len(correlation_normalized) // 100)  # Adaptive window size
                
                start_idx = max(0, i - window)
                end_idx = min(len(correlation_normalized), i + window)
                
                for j in range(start_idx, end_idx):
                    if j != i and correlation_normalized[j] > correlation_normalized[i]:
                        is_peak = False
                        break
                
                if is_peak:
                    start_time = i / sample_rate
                    end_time = (i + jingle_duration_samples) / sample_rate
                    
                    # Only add if it doesn't overlap with existing matches
                    overlap = False
                    for match in occurrences:
                        if (start_time <= match['end'] and end_time >= match['start']):
                            overlap = True
                            # Keep the one with higher confidence
                            if correlation_normalized[i] > match['confidence']:
                                match['start'] = start_time
                                match['end'] = end_time
                                match['confidence'] = float(correlation_normalized[i])
                            break
                    
                    if not overlap:
                        occurrences.append({
                            "start": float(start_time),
                            "end": float(end_time),
                            "confidence": float(correlation_normalized[i])
                        })
        
        logger.info(f"Found {len(occurrences)} potential matches")
        
        # Sort by confidence
        occurrences.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Limit to top 5 matches
        occurrences = occurrences[:5]
        
        return occurrences
        
    except Exception as e:
        logger.error(f"Error in jingle detection: {e}", exc_info=True)
        return []