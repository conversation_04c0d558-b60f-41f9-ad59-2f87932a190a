import os
import time
import json
import uuid
import logging
import threading
import queue
from dataclasses import dataclass, asdict
import numpy as np
import soundfile as sf
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import processing functions from audio_utils
from audio_utils import (
    calculate_lufs, create_lufs_graph, normalize_audio_loudness,
    apply_compression, is_stereo, calculate_true_peak, apply_true_peak_limiter,
    normalize_audio_with_true_peak_protection, apply_peak_limiter
)

# Import audio search functionality
from audio_search_hybrid import find_jingle_occurrences_hybrid

@dataclass
class Task:
    id: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    progress: float  # 0.0 to 1.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def to_dict(self):
        return asdict(self)

class AudioProcessor:
    def __init__(self, upload_folder):
        self.task_queue = queue.Queue()
        self.tasks = {}  # Store task status by ID
        self.upload_folder = upload_folder
        self.worker_thread = threading.Thread(target=self._process_queue, daemon=True)
        self.worker_thread.start()
        logger.info("Audio processor worker started")
    
    def add_task(self, filepath, params):
        """Add a new audio processing task to the queue"""
        task_id = str(uuid.uuid4())
        task = Task(id=task_id, status='pending', progress=0.0)
        self.tasks[task_id] = task
        
        # Add to queue
        self.task_queue.put((task_id, filepath, params))
        logger.info(f"Added task {task_id} to queue for file {filepath}")
        
        return task_id
    
    def get_task_status(self, task_id):
        """Get the current status of a task"""
        logger.info(f"Getting status for task: {task_id}, available tasks: {list(self.tasks.keys())}")
        if task_id in self.tasks:
            task_dict = self.tasks[task_id].to_dict()
            logger.info(f"Task found: {task_dict}")
            return task_dict
        logger.warning(f"Task not found: {task_id}")
        return None
    
    def _update_progress(self, task_id, progress, status=None):
        """Update task progress"""
        if task_id in self.tasks:
            self.tasks[task_id].progress = progress
            if status:
                self.tasks[task_id].status = status
    
    def _process_queue(self):
        """Worker thread to process the queue"""
        while True:
            try:
                # Get task from queue
                task_id, filepath, params = self.task_queue.get()
                
                if task_id not in self.tasks:
                    logger.warning(f"Task {task_id} not found in tasks dict")
                    self.task_queue.task_done()
                    continue
                
                # Update status
                self.tasks[task_id].status = 'processing'
                logger.info(f"Processing task {task_id}")
                
                try:
                    # Process the audio file
                    result = self._process_audio(task_id, filepath, params)
                    
                    # Update task with result
                    self.tasks[task_id].status = 'completed'
                    self.tasks[task_id].progress = 1.0
                    self.tasks[task_id].result = result
                    logger.info(f"Task {task_id} completed successfully")
                    
                except Exception as e:
                    logger.error(f"Error processing task {task_id}: {str(e)}", exc_info=True)
                    self.tasks[task_id].status = 'failed'
                    self.tasks[task_id].error = str(e)
                
                # Mark task as done
                self.task_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker thread error: {str(e)}", exc_info=True)
                time.sleep(1)  # Prevent tight loop on error
    
    def _process_audio(self, task_id, filepath, params):
        """Process an audio file with the given parameters"""
        try:
            # Load audio data
            self._update_progress(task_id, 0.1)
            audio_data, sample_rate = sf.read(filepath)
            logger.info(f"Audio loaded: shape={audio_data.shape}, rate={sample_rate}")
            
            # Get audio info
            is_stereo_audio = is_stereo(audio_data)
            
            # Calculate original LUFS and true peak
            self._update_progress(task_id, 0.2)
            original_lufs = calculate_lufs(audio_data, sample_rate)
            if original_lufs is None:
                raise ValueError("Failed to calculate original LUFS")

            original_true_peak = calculate_true_peak(audio_data, sample_rate)
            logger.info(f"Original true peak: {original_true_peak:.2f} dBTP")
            
            # Create original LUFS graph
            self._update_progress(task_id, 0.3)
            original_graph_data = create_lufs_graph(audio_data, sample_rate)
            if original_graph_data is None:
                raise ValueError("Failed to create original LUFS graph")

            # Prepare result
            result = {
                'lufs': float(original_lufs),  # Ensure it's a float
                'original_true_peak': float(original_true_peak),  # Add true peak measurement
                'original_graph_data': original_graph_data,
                'is_stereo': is_stereo_audio,
                'sample_rate': sample_rate,
                'duration': len(audio_data[0]) / sample_rate if is_stereo_audio else len(audio_data) / sample_rate
            }

            # Perform jingle detection if requested
            if params.get('nrk_podcast_check'):
                self._update_progress(task_id, 0.35)
                logger.info("Performing NRK podcast jingle detection")
                try:
                    # Get the absolute path to the jingle file
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    jingle_path = os.path.join(current_dir, 'jingles', 'En podkast - Mann.wav')
                    logger.info(f"Using jingle file: {jingle_path}")

                    jingle_matches = find_jingle_occurrences_hybrid(audio_data, sample_rate, jingle_path, threshold=0.75, search_duration=4.0)
                    result['jingle_matches'] = jingle_matches
                    logger.info(f"Found {len(jingle_matches)} jingle matches")
                except Exception as e:
                    logger.error(f"Error during jingle detection: {e}", exc_info=True)
                    result['jingle_matches'] = []
            
            # Check if we need to process the audio
            target_lufs = params.get('max_lufs')
            if target_lufs:
                try:
                    target_lufs = float(target_lufs)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid target LUFS: {target_lufs}, using default -14")
                    target_lufs = -14.0
            else:
                target_lufs = -14.0
            
            # Process audio if needed
            if abs(original_lufs - target_lufs) > 0.5 or params.get('compression'):
                # Copy audio for processing
                processed_audio = np.copy(audio_data)

                # Step 1: Apply compression if requested
                if params.get('compression'):
                    self._update_progress(task_id, 0.35)

                    # Get compression parameters
                    threshold = float(params.get('threshold', -20))
                    ratio = float(params.get('ratio', 4))
                    attack = float(params.get('attack', 5))
                    release = float(params.get('release', 50))

                    logger.info("Step 1: Applying compression")
                    processed_audio = apply_compression(
                        processed_audio, sample_rate, threshold, ratio, attack, release
                    )

                    if processed_audio is None:
                        raise ValueError("Failed to apply compression")

                # Step 1.5: Apply FIRST peak limiting after compression
                self._update_progress(task_id, 0.4)
                logger.info("=== STEP 1.5: FIRST PEAK LIMITING ===")
                logger.info("Applying first peak limiter to reduce peaks by 3dB")

                # Measure peaks before first limiting
                original_peak = np.max(np.abs(processed_audio))
                logger.info(f"Peak level before first limiting: {20 * np.log10(original_peak):.2f} dB")

                processed_audio = apply_peak_limiter(processed_audio, sample_rate, peak_reduction_db=3.0)

                if processed_audio is None:
                    raise ValueError("Failed to apply first peak limiting")

                # Measure peaks after first limiting
                limited_peak = np.max(np.abs(processed_audio))
                logger.info(f"Peak level after first limiting: {20 * np.log10(limited_peak):.2f} dB")

                if original_peak > 0 and limited_peak > 0:
                    actual_reduction = 20 * np.log10(original_peak / limited_peak)
                    logger.info(f"FIRST PEAK REDUCTION ACHIEVED: {actual_reduction:.2f} dB")
                logger.info("=== END FIRST PEAK LIMITING ===")


                # Step 2: Apply LUFS normalization with integrated true peak protection
                self._update_progress(task_id, 0.5)
                logger.info("Step 2: Applying LUFS normalization with true peak protection")

                processed_audio = normalize_audio_with_true_peak_protection(
                    processed_audio, sample_rate, target_lufs, max_true_peak_db=-1.0
                )

                if processed_audio is None:
                    raise ValueError("Failed to normalize audio with true peak protection")

                # Step 3: Apply SECOND peak limiting for final peak control
                self._update_progress(task_id, 0.6)
                logger.info("=== STEP 3: SECOND PEAK LIMITING ===")
                logger.info("Applying second peak limiter for final peak control (3dB reduction)")

                # Measure peaks before second limiting
                pre_second_peak = np.max(np.abs(processed_audio))
                logger.info(f"Peak level before second limiting: {20 * np.log10(pre_second_peak):.2f} dB")

                processed_audio = apply_peak_limiter(processed_audio, sample_rate, peak_reduction_db=3.0)

                if processed_audio is None:
                    raise ValueError("Failed to apply second peak limiting")

                # Measure peaks after second limiting
                post_second_peak = np.max(np.abs(processed_audio))
                logger.info(f"Peak level after second limiting: {20 * np.log10(post_second_peak):.2f} dB")

                if pre_second_peak > 0 and post_second_peak > 0:
                    second_reduction = 20 * np.log10(pre_second_peak / post_second_peak)
                    logger.info(f"SECOND PEAK REDUCTION ACHIEVED: {second_reduction:.2f} dB")
                logger.info("=== END SECOND PEAK LIMITING ===")

                # Step 4: Final safety verification and limiting if needed
                self._update_progress(task_id, 0.7)
                final_true_peak = calculate_true_peak(processed_audio, sample_rate)
                logger.info(f"Final verification: True peak = {final_true_peak:.2f} dBTP")

                if final_true_peak > -1.0:
                    logger.warning(f"Final true peak {final_true_peak:.2f} dBTP still exceeds -1 dBTP, applying emergency limiting")
                    processed_audio = apply_true_peak_limiter(processed_audio, sample_rate, max_true_peak_db=-1.0)

                    if processed_audio is None:
                        raise ValueError("Failed to apply emergency true peak limiting")

                    # Final verification
                    emergency_true_peak = calculate_true_peak(processed_audio, sample_rate)
                    logger.info(f"After emergency limiting: True peak = {emergency_true_peak:.2f} dBTP")

                # Calculate processed LUFS and true peak
                self._update_progress(task_id, 0.75)
                processed_lufs = calculate_lufs(processed_audio, sample_rate)
                processed_true_peak = calculate_true_peak(processed_audio, sample_rate)

                logger.info(f"=== FINAL AUDIO METRICS ===")
                logger.info(f"  LUFS: {processed_lufs:.2f}")
                logger.info(f"  True Peak: {processed_true_peak:.2f} dBTP")
                logger.info(f"  Target LUFS: {target_lufs:.2f}")
                logger.info(f"=== PROCESSING SUMMARY ===")
                logger.info(f"  Two-stage peak limiting applied")
                logger.info(f"  Total peak reduction: ~6dB (3dB + 3dB)")
                logger.info(f"  Final result: Maximum volume with peak control")

                # Create processed LUFS graph
                self._update_progress(task_id, 0.8)
                processed_graph_data = create_lufs_graph(processed_audio, sample_rate)
                if processed_graph_data is None:
                    raise ValueError("Failed to create processed LUFS graph")
                
                # Save processed audio to file
                self._update_progress(task_id, 0.9)
                filename = os.path.basename(filepath)
                name, ext = os.path.splitext(filename)
                processed_filename = f"processed_{task_id}_{name}.wav"
                processed_filepath = os.path.join(self.upload_folder, processed_filename)
                
                sf.write(processed_filepath, processed_audio, sample_rate)
                
                # Update result
                result.update({
                    'processed_lufs': float(processed_lufs),  # Ensure it's a float
                    'processed_true_peak': float(processed_true_peak),  # Add processed true peak
                    'processed_file': processed_filename,
                    'processed_graph_data': processed_graph_data
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}", exc_info=True)
            raise
