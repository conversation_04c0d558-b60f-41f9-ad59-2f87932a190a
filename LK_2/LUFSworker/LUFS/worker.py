import os
import time
import json
import uuid
import logging
import threading
import queue
from dataclasses import dataclass, asdict
import numpy as np
import soundfile as sf
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import processing functions from audio_utils
from audio_utils import (
    calculate_lufs, create_lufs_graph, normalize_audio_loudness,
    apply_compression, is_stereo, calculate_true_peak, apply_true_peak_limiter,
    normalize_audio_with_true_peak_protection, apply_peak_limiter
)

# Import audio search functionality
from audio_search_hybrid import find_jingle_occurrences_hybrid

@dataclass
class Task:
    id: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    progress: float  # 0.0 to 1.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def to_dict(self):
        return asdict(self)

class AudioProcessor:
    def __init__(self, upload_folder):
        self.task_queue = queue.Queue()
        self.tasks = {}  # Store task status by ID
        self.upload_folder = upload_folder
        self.worker_thread = threading.Thread(target=self._process_queue, daemon=True)
        self.worker_thread.start()
        logger.info("Audio processor worker started")
    
    def add_task(self, filepath, params):
        """Add a new audio processing task to the queue"""
        task_id = str(uuid.uuid4())
        task = Task(id=task_id, status='pending', progress=0.0)
        self.tasks[task_id] = task
        
        # Add to queue
        self.task_queue.put((task_id, filepath, params))
        logger.info(f"Added task {task_id} to queue for file {filepath}")
        
        return task_id
    
    def get_task_status(self, task_id):
        """Get the current status of a task"""
        logger.info(f"Getting status for task: {task_id}, available tasks: {list(self.tasks.keys())}")
        if task_id in self.tasks:
            task_dict = self.tasks[task_id].to_dict()
            logger.info(f"Task found: {task_dict}")
            return task_dict
        logger.warning(f"Task not found: {task_id}")
        return None
    
    def _update_progress(self, task_id, progress, status=None):
        """Update task progress"""
        if task_id in self.tasks:
            self.tasks[task_id].progress = progress
            if status:
                self.tasks[task_id].status = status
    
    def _process_queue(self):
        """Worker thread to process the queue"""
        while True:
            try:
                # Get task from queue
                task_id, filepath, params = self.task_queue.get()
                
                if task_id not in self.tasks:
                    logger.warning(f"Task {task_id} not found in tasks dict")
                    self.task_queue.task_done()
                    continue
                
                # Update status
                self.tasks[task_id].status = 'processing'
                logger.info(f"Processing task {task_id}")
                
                try:
                    # Process the audio file
                    result = self._process_audio(task_id, filepath, params)
                    
                    # Update task with result
                    self.tasks[task_id].status = 'completed'
                    self.tasks[task_id].progress = 1.0
                    self.tasks[task_id].result = result
                    logger.info(f"Task {task_id} completed successfully")
                    
                except Exception as e:
                    logger.error(f"Error processing task {task_id}: {str(e)}", exc_info=True)
                    self.tasks[task_id].status = 'failed'
                    self.tasks[task_id].error = str(e)
                
                # Mark task as done
                self.task_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker thread error: {str(e)}", exc_info=True)
                time.sleep(1)  # Prevent tight loop on error
    
    def _process_audio(self, task_id, filepath, params):
        """Process an audio file with the given parameters"""
        try:
            # Load audio data
            self._update_progress(task_id, 0.1)
            audio_data, sample_rate = sf.read(filepath)
            logger.info(f"Audio loaded: shape={audio_data.shape}, rate={sample_rate}")
            
            # Get audio info
            is_stereo_audio = is_stereo(audio_data)
            
            # Calculate original LUFS and true peak
            self._update_progress(task_id, 0.2)
            original_lufs = calculate_lufs(audio_data, sample_rate)
            if original_lufs is None:
                raise ValueError("Failed to calculate original LUFS")

            original_true_peak = calculate_true_peak(audio_data, sample_rate)
            logger.info(f"Original true peak: {original_true_peak:.2f} dBTP")
            
            # Create original LUFS graph
            self._update_progress(task_id, 0.3)
            original_graph_data = create_lufs_graph(audio_data, sample_rate)
            if original_graph_data is None:
                raise ValueError("Failed to create original LUFS graph")

            # Prepare result
            result = {
                'lufs': float(original_lufs),  # Ensure it's a float
                'original_true_peak': float(original_true_peak),  # Add true peak measurement
                'original_graph_data': original_graph_data,
                'is_stereo': is_stereo_audio,
                'sample_rate': sample_rate,
                'duration': len(audio_data[0]) / sample_rate if is_stereo_audio else len(audio_data) / sample_rate
            }

            # Perform jingle detection if requested
            if params.get('nrk_podcast_check'):
                self._update_progress(task_id, 0.35)
                logger.info("Performing NRK podcast jingle detection")
                try:
                    # Get the absolute path to the jingle file
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    jingle_path = os.path.join(current_dir, 'jingles', 'En podkast - Mann.wav')
                    logger.info(f"Using jingle file: {jingle_path}")

                    jingle_matches = find_jingle_occurrences_hybrid(audio_data, sample_rate, jingle_path, threshold=0.75, search_duration=4.0)
                    result['jingle_matches'] = jingle_matches
                    logger.info(f"Found {len(jingle_matches)} jingle matches")
                except Exception as e:
                    logger.error(f"Error during jingle detection: {e}", exc_info=True)
                    result['jingle_matches'] = []
            
            # Check if we need to process the audio
            target_lufs = params.get('max_lufs')
            if target_lufs:
                try:
                    target_lufs = float(target_lufs)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid target LUFS: {target_lufs}, using default -14")
                    target_lufs = -14.0
            else:
                target_lufs = -14.0
            
            # Process audio if needed
            if abs(original_lufs - target_lufs) > 0.5 or params.get('compression'):
                # Copy audio for processing
                processed_audio = np.copy(audio_data)

                # Step 1: Apply AGGRESSIVE compression for maximum volume
                if params.get('compression'):
                    self._update_progress(task_id, 0.3)

                    # Get compression parameters (more aggressive settings)
                    threshold = float(params.get('threshold', -20))
                    ratio = float(params.get('ratio', 4.0))
                    attack = float(params.get('attack', 5.0))
                    release = float(params.get('release', 50.0))

                    logger.info("=== STEP 1: AGGRESSIVE COMPRESSION ===")
                    logger.info(f"Applying aggressive compression: threshold={threshold}dB, ratio={ratio}:1, attack={attack}ms, release={release}ms")

                    # Measure levels before compression
                    pre_comp_peak = np.max(np.abs(processed_audio))
                    pre_comp_lufs = calculate_lufs(processed_audio, sample_rate)
                    logger.info(f"Before compression - Peak: {20 * np.log10(pre_comp_peak):.2f} dB, LUFS: {pre_comp_lufs:.2f}")

                    processed_audio = apply_compression(
                        processed_audio, sample_rate, threshold, ratio, attack, release
                    )

                    if processed_audio is None:
                        raise ValueError("Failed to apply compression")

                    # Measure levels after compression
                    post_comp_peak = np.max(np.abs(processed_audio))
                    post_comp_lufs = calculate_lufs(processed_audio, sample_rate)
                    logger.info(f"After compression - Peak: {20 * np.log10(post_comp_peak):.2f} dB, LUFS: {post_comp_lufs:.2f}")
                    logger.info("=== END AGGRESSIVE COMPRESSION ===")

                # Step 2: Push audio closer to 0dB for maximum volume
                self._update_progress(task_id, 0.4)
                logger.info("=== STEP 2: PUSH TO NEAR 0dB ===")
                logger.info("Pushing compressed audio closer to 0dB for maximum volume")

                # Find current peak and calculate gain to push close to 0dB
                current_peak = np.max(np.abs(processed_audio))
                current_peak_db = 20 * np.log10(current_peak) if current_peak > 0 else -np.inf

                # Target peak level (leave significant headroom for intersample peaks)
                target_peak_db = -6.0  # Push to -6dB, leaving room for intersample peaks and true peak limiting
                gain_needed = target_peak_db - current_peak_db
                gain_linear = 10 ** (gain_needed / 20)

                logger.info(f"Current sample peak: {current_peak_db:.2f} dB")
                logger.info(f"Target sample peak: {target_peak_db:.2f} dB")
                logger.info(f"Gain boost needed: {gain_needed:.2f} dB")

                # Check current true peak before boosting
                current_true_peak = calculate_true_peak(processed_audio, sample_rate)
                logger.info(f"Current true peak BEFORE boost: {current_true_peak:.2f} dBTP")

                # Apply gain boost
                processed_audio = processed_audio * gain_linear

                # Verify the result
                boosted_peak = np.max(np.abs(processed_audio))
                boosted_peak_db = 20 * np.log10(boosted_peak) if boosted_peak > 0 else -np.inf
                boosted_lufs = calculate_lufs(processed_audio, sample_rate)
                boosted_true_peak = calculate_true_peak(processed_audio, sample_rate)

                logger.info(f"After boost - Sample Peak: {boosted_peak_db:.2f} dB, LUFS: {boosted_lufs:.2f}")
                logger.info(f"After boost - TRUE PEAK: {boosted_true_peak:.2f} dBTP")

                if boosted_true_peak > 0.0:
                    logger.warning(f"⚠️ TRUE PEAK ABOVE 0dB: {boosted_true_peak:.2f} dBTP - True peak limiting will be needed!")

                logger.info("=== END PUSH TO NEAR 0dB ===")

                # Step 3: Apply True Peak Limiting as ONLY peak control stage
                self._update_progress(task_id, 0.5)
                logger.info("=== STEP 3: TRUE PEAK LIMITING (ONLY PEAK CONTROL) ===")
                logger.info("Applying true peak limiting as the single peak control stage (-1 dBTP)")

                # Measure true peak before limiting
                pre_true_peak = calculate_true_peak(processed_audio, sample_rate)
                pre_sample_peak = np.max(np.abs(processed_audio))
                pre_sample_peak_db = 20 * np.log10(pre_sample_peak) if pre_sample_peak > 0 else -np.inf

                logger.info(f"Before true peak limiting:")
                logger.info(f"  Sample peak: {pre_sample_peak_db:.2f} dB")
                logger.info(f"  True peak: {pre_true_peak:.2f} dBTP")

                # Apply true peak limiting (this will handle all peak control)
                processed_audio = apply_true_peak_limiter(processed_audio, sample_rate, max_true_peak_db=-1.0)

                if processed_audio is None:
                    raise ValueError("Failed to apply true peak limiting")

                # Verify true peak limiting results
                post_true_peak = calculate_true_peak(processed_audio, sample_rate)
                post_sample_peak = np.max(np.abs(processed_audio))
                post_sample_peak_db = 20 * np.log10(post_sample_peak) if post_sample_peak > 0 else -np.inf
                post_lufs = calculate_lufs(processed_audio, sample_rate)

                logger.info(f"After true peak limiting:")
                logger.info(f"  Sample peak: {post_sample_peak_db:.2f} dB")
                logger.info(f"  True peak: {post_true_peak:.2f} dBTP")
                logger.info(f"  LUFS: {post_lufs:.2f}")

                # Calculate the actual peak reduction achieved
                if pre_sample_peak > 0 and post_sample_peak > 0:
                    peak_reduction = pre_sample_peak_db - post_sample_peak_db
                    logger.info(f"  Peak reduction achieved: {peak_reduction:.2f} dB")

                logger.info("=== END TRUE PEAK LIMITING ===")

                # Step 4: Apply LUFS normalization as FINAL step (optional fine-tuning)
                self._update_progress(task_id, 0.6)
                logger.info("=== STEP 4: FINAL LUFS NORMALIZATION (OPTIONAL) ===")

                # Check if we need LUFS adjustment
                current_lufs = calculate_lufs(processed_audio, sample_rate)
                lufs_difference = abs(current_lufs - target_lufs)

                logger.info(f"Current LUFS: {current_lufs:.2f}")
                logger.info(f"Target LUFS: {target_lufs:.2f}")
                logger.info(f"Difference: {lufs_difference:.2f} dB")

                if lufs_difference > 1.0:  # Only adjust if significantly off target
                    logger.info(f"LUFS difference > 1dB, applying final normalization")

                    # Apply LUFS normalization WITHOUT true peak protection (since we already handled it)
                    processed_audio = normalize_audio_loudness(processed_audio, sample_rate, target_lufs)

                    if processed_audio is None:
                        raise ValueError("Failed to apply final LUFS normalization")

                    # Measure LUFS after final normalization
                    post_final_lufs = calculate_lufs(processed_audio, sample_rate)
                    logger.info(f"LUFS after final normalization: {post_final_lufs:.2f}")
                    logger.info(f"LUFS accuracy: {abs(post_final_lufs - target_lufs):.2f} dB from target")

                    # Final true peak check (should still be safe)
                    final_true_peak_check = calculate_true_peak(processed_audio, sample_rate)
                    logger.info(f"Final true peak verification: {final_true_peak_check:.2f} dBTP")

                    if final_true_peak_check > -0.5:
                        logger.warning(f"LUFS normalization pushed true peak to {final_true_peak_check:.2f} dBTP")
                else:
                    logger.info(f"LUFS already close to target ({lufs_difference:.2f} dB), skipping final normalization")
                    logger.info("Compression and boost achieved good LUFS level naturally")

                logger.info("=== END FINAL LUFS NORMALIZATION ===")

                # Calculate processed LUFS and true peak
                self._update_progress(task_id, 0.75)
                processed_lufs = calculate_lufs(processed_audio, sample_rate)
                processed_true_peak = calculate_true_peak(processed_audio, sample_rate)

                logger.info(f"=== FINAL AUDIO METRICS ===")
                logger.info(f"  LUFS: {processed_lufs:.2f}")
                logger.info(f"  True Peak: {processed_true_peak:.2f} dBTP")
                logger.info(f"  Target LUFS: {target_lufs:.2f}")
                logger.info(f"=== PROCESSING SUMMARY ===")
                logger.info(f"  Two-stage peak limiting applied")
                logger.info(f"  Total peak reduction: ~8dB (3dB + 5dB)")
                logger.info(f"  Final result: Maximum volume with aggressive peak control")

                # Create processed LUFS graph
                self._update_progress(task_id, 0.8)
                processed_graph_data = create_lufs_graph(processed_audio, sample_rate)
                if processed_graph_data is None:
                    raise ValueError("Failed to create processed LUFS graph")
                
                # Save processed audio to file
                self._update_progress(task_id, 0.9)
                filename = os.path.basename(filepath)
                name, ext = os.path.splitext(filename)
                processed_filename = f"processed_{task_id}_{name}.wav"
                processed_filepath = os.path.join(self.upload_folder, processed_filename)
                
                sf.write(processed_filepath, processed_audio, sample_rate)
                
                # Update result
                result.update({
                    'processed_lufs': float(processed_lufs),  # Ensure it's a float
                    'processed_true_peak': float(processed_true_peak),  # Add processed true peak
                    'processed_file': processed_filename,
                    'processed_graph_data': processed_graph_data
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}", exc_info=True)
            raise
