// Global variables for tracking task status
let currentTaskId = null;
const progressContainer = document.getElementById('progress-container');
const progressBar = document.getElementById('progress-bar');
const statusMessage = document.getElementById('status-message');
const resultsDiv = document.getElementById('results');

// Get form elements
const uploadForm = document.getElementById('audio-form');
const uploadButton = document.getElementById('upload-button');
const spinner = document.getElementById('spinner');
const compressionCheckbox = document.getElementById('compression');
const compressionOptions = document.getElementById('compression-options');
const compressionInfoBtn = document.getElementById('compression-info-btn');
const compressionInfo = document.getElementById('compression-info');
const nrkPodcastCheck = document.getElementById('nrk-podcast-check');

// Show/hide compression options
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    
    // Check if Chart.js is loaded
    if (typeof Chart !== 'undefined') {
        console.log('Chart.js is loaded');
    } else {
        console.error('Chart.js is not loaded');
    }
    
    // Set up compression checkbox (simplified - no options to show/hide)
    if (compressionCheckbox) {
        console.log('Smart compression checkbox found');
    }
    
    // Set up compression info button
    if (compressionInfoBtn && compressionInfo) {
        compressionInfoBtn.addEventListener('click', function(e) {
            e.preventDefault();
            compressionInfo.classList.toggle('hidden');
        });
    }
    
    // Set up the round knobs
    setupLufsKnob();
    setupThresholdKnob();

    // Set up fixed compression settings (attack, release, ratio)
    setupFixedCompression();
    
    // Debug form elements
    console.log('Upload form:', uploadForm);
    console.log('Upload button:', uploadButton);
    console.log('Compression checkbox:', compressionCheckbox);
    console.log('Compression options:', compressionOptions);
    console.log('Compression info button:', compressionInfoBtn);
    console.log('Compression info:', compressionInfo);
});

// Helper function to set up range sliders
function setupRangeSlider(sliderId, tooltipId, suffix) {
    const slider = document.getElementById(sliderId);
    const tooltip = document.getElementById(tooltipId);

    if (slider && tooltip) {
        function updateTooltip() {
            const value = parseFloat(slider.value);
            const min = parseFloat(slider.min);
            const max = parseFloat(slider.max);

            // Update text content of the tooltip
            tooltip.textContent = slider.value + suffix;

            // Calculate the percentage position
            // This works because the slider's parent (.slider-wrapper) is position: relative.
            const percent = (value - min) / (max - min);
            
            // Position the tooltip.
            // 'left' is a percentage of the parent .slider-wrapper's width.
            // transform: translateX(-50%) centers the tooltip on this percentage point.
            tooltip.style.left = `${percent * 100}%`;
        }

        // Set initial tooltip text and position
        updateTooltip();
        
        // Update tooltip on input
        slider.addEventListener('input', updateTooltip);

        // Update on window resize if slider width changes (good practice)
        window.addEventListener('resize', updateTooltip);
    } else {
        if (!slider) console.error(`Slider with ID '${sliderId}' not found.`);
        if (!tooltip) console.error(`Tooltip with ID '${tooltipId}' not found.`);
    }
}

// Form submission handler
if (uploadForm) {
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted');
        
        // Show progress container
        if (progressContainer) {
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            statusMessage.textContent = 'Uploading file...';
        }
        
        // Hide results if they were previously shown
        if (resultsDiv) {
            resultsDiv.style.display = 'none';
        }
        
        // Get file input
        const fileInput = document.getElementById('file');
        if (!fileInput || !fileInput.files || !fileInput.files[0]) {
            showError('Please select an audio file.');
            if (progressContainer) progressContainer.style.display = 'none';
            return;
        }
        
        // Get target LUFS
        const targetLufs = document.getElementById('target_lufs')?.value;
        if (!targetLufs) {
            showError('Please enter a target LUFS value.');
            if (progressContainer) progressContainer.style.display = 'none';
            return;
        }
        
        // Disable button and show spinner
        if (uploadButton) {
            uploadButton.disabled = true;
            const buttonText = document.getElementById('button-text');
            if (buttonText) buttonText.textContent = 'Processing...';
        }
        if (spinner) spinner.style.display = 'inline-flex';
        
        const formData = new FormData(this);
        
        // Add NRK podcast check value
        if (nrkPodcastCheck && nrkPodcastCheck.checked) {
            formData.append('nrk_podcast_check', 'true');
        }
        
        // Submit the form
        fetch('/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Form submission response:', data);
            
            if (data.error) {
                showError(data.error);
                if (progressContainer) progressContainer.style.display = 'none';
                
                // Re-enable button
                if (uploadButton) {
                    uploadButton.disabled = false;
                    const buttonText = document.getElementById('button-text');
                    if (buttonText) buttonText.textContent = 'Analyze Audio';
                }
                if (spinner) spinner.style.display = 'none';
                return;
            }
            
            // Store the task ID and start polling
            currentTaskId = data.task_id;
            statusMessage.textContent = 'Processing audio...';
            pollTaskStatus();
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            showError('Error submitting form: ' + error);
            if (progressContainer) progressContainer.style.display = 'none';
            
            // Re-enable button
            if (uploadButton) {
                uploadButton.disabled = false;
                const buttonText = document.getElementById('button-text');
                if (buttonText) buttonText.textContent = 'Analyze Audio';
            }
            if (spinner) spinner.style.display = 'none';
        });
    });
} else {
    console.error('Upload form not found');
}

function pollTaskStatus() {
    if (!currentTaskId) {
        console.error('No task ID available for polling');
        return;
    }
    
    console.log('Polling task status for:', currentTaskId);
    
    fetch(`/task/${currentTaskId}`)
        .then(response => response.json())
        .then(data => {
            console.log('Task status update:', data);
            
            if (data.error) {
                showError(data.error);
                progressContainer.style.display = 'none';
                return;
            }
            
            // Update progress bar
            const progressPercent = Math.round(data.progress * 100);
            progressBar.style.width = `${progressPercent}%`;
            progressBar.textContent = `${progressPercent}%`;
            
            // Update status message
            switch (data.status) {
                case 'pending':
                    statusMessage.textContent = 'Waiting in queue...';
                    break;
                case 'processing':
                    statusMessage.textContent = 'Processing audio...';
                    break;
                case 'completed':
                    statusMessage.textContent = 'Processing complete!';
                    // Re-enable button and hide spinner
                    if (uploadButton) {
                        uploadButton.disabled = false;
                        const buttonText = document.getElementById('button-text');
                        if (buttonText) buttonText.textContent = 'Analyze Audio';
                    }
                    if (spinner) spinner.style.display = 'none';

                    // Display results
                    try {
                        displayResults(data.result);
                    } catch (err) {
                        console.error('Error displaying results:', err);
                        showError('Error displaying results: ' + err.message);
                    }
                    return; // Stop polling
                case 'failed':
                    showError('Processing failed: ' + (data.error || 'Unknown error'));
                    progressContainer.style.display = 'none';
                    return; // Stop polling
                default:
                    statusMessage.textContent = `Status: ${data.status}`;
            }
            
            // Continue polling
            setTimeout(pollTaskStatus, 1000);
        })
        .catch(error => {
            console.error('Error checking task status:', error);
            showError('Error checking task status: ' + error);
            setTimeout(pollTaskStatus, 2000); // Retry with longer delay
        });
}

function displayResults(data) {
    console.log('Displaying results:', data);
    
    if (!data) {
        console.error('No result data to display');
        return;
    }
    
    // Hide progress container
    progressContainer.style.display = 'none';
    
    // Show results container
    if (resultsDiv) {
        resultsDiv.style.display = 'block';
    }
    
    // Update LUFS values
    const lufsValue = document.getElementById('lufs-value');
    if (lufsValue && data.lufs !== undefined) {
        // Check if lufs is a number before using toFixed
        if (typeof data.lufs === 'number') {
            lufsValue.textContent = data.lufs.toFixed(1) + ' LUFS';
        } else {
            lufsValue.textContent = data.lufs + ' LUFS';
            console.warn('LUFS value is not a number:', data.lufs);
        }
    }

    // Update True Peak values
    const truePeakValue = document.getElementById('true-peak-value');
    if (truePeakValue && data.original_true_peak !== undefined) {
        const peak = data.original_true_peak;
        const isOverLimit = peak > -1.0;
        const safetyIcon = isOverLimit ? '⚠️' : '✅';
        const safetyClass = isOverLimit ? 'text-danger' : 'text-success';
        truePeakValue.innerHTML = `<span class="${safetyClass}">${peak.toFixed(2)} dBTP ${safetyIcon}</span>`;
    }

    const processedLufsValue = document.getElementById('processed-lufs-value');
    const processedTruePeakValue = document.getElementById('processed-true-peak-value');
    const processedLufsContainer = document.getElementById('processed-lufs-container');

    if (processedLufsValue && processedLufsContainer) {
        if (data.processed_lufs !== undefined) {
            // Check if processed_lufs is a number before using toFixed
            if (typeof data.processed_lufs === 'number') {
                processedLufsValue.textContent = data.processed_lufs.toFixed(1) + ' LUFS';
            } else {
                processedLufsValue.textContent = data.processed_lufs + ' LUFS';
                console.warn('Processed LUFS value is not a number:', data.processed_lufs);
            }

            // Update processed true peak
            if (processedTruePeakValue && data.processed_true_peak !== undefined) {
                const peak = data.processed_true_peak;
                const isOverLimit = peak > -1.0;
                const safetyIcon = isOverLimit ? '⚠️' : '✅';
                const safetyClass = isOverLimit ? 'text-danger' : 'text-success';
                processedTruePeakValue.innerHTML = `<span class="${safetyClass}">${peak.toFixed(2)} dBTP ${safetyIcon}</span>`;
            }

            processedLufsContainer.style.display = 'block';
        } else {
            processedLufsContainer.style.display = 'none';
        }
    }
    
    // Update download link
    const downloadLink = document.getElementById('download-link');
    if (downloadLink) {
        if (data.processed_file) {
            downloadLink.href = `/uploads/${data.processed_file}`;
            downloadLink.style.display = 'inline-block';
            console.log('Download link set to:', downloadLink.href);
        } else {
            downloadLink.style.display = 'none';
            console.log('No processed file available, hiding download link');
        }
    }
    
    // Add jingle detection results to main results area if NRK check was enabled
    const nrkPodcastCheck = document.getElementById('nrk-podcast-check');
    if (nrkPodcastCheck && nrkPodcastCheck.checked) {
        addJingleResultsCard(data.jingle_matches);
    }

    // Hide the separate jingle results section since we're integrating into main results
    const jingleResultsContainer = document.getElementById('jingle-results');
    if (jingleResultsContainer) {
        jingleResultsContainer.style.display = 'none';
    }
    
    // Render comparison graph
    const comparisonCanvas = document.getElementById('comparison-graph');
    if (comparisonCanvas && data.original_graph_data) {
        renderComparisonGraph(
            comparisonCanvas,
            data.original_graph_data,
            data.processed_graph_data,
            'LUFS Comparison'
        );
    }
}

function drawLufsGraph(containerId, data) {
    if (!data || !data.times || !data.lufs || data.times.length === 0) {
        console.error('Invalid graph data for', containerId);
        return;
    }
    
    console.log(`Drawing graph in ${containerId} with ${data.times.length} data points`);
    
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('Graph container not found:', containerId);
        return;
    }
    
    // Clear previous content
    container.innerHTML = '';
    
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.width = container.clientWidth || 400;
    canvas.height = 200;
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // Set up the graph
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Find min and max values for scaling
    const minLufs = Math.min(...data.lufs);
    const maxLufs = Math.max(...data.lufs);
    const range = Math.max(Math.abs(maxLufs - minLufs), 10); // Ensure at least 10dB range
    
    // Draw axes
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(40, 10);
    ctx.lineTo(40, canvas.height - 30);
    ctx.lineTo(canvas.width - 10, canvas.height - 30);
    ctx.stroke();
    
    // Draw LUFS line
    ctx.strokeStyle = '#4CAF50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const timeScale = (canvas.width - 50) / (data.times[data.times.length - 1] || 1);
    const lufsScale = (canvas.height - 40) / range;
    
    for (let i = 0; i < data.times.length; i++) {
        const x = 40 + data.times[i] * timeScale;
        // Invert Y axis since canvas 0 is at top
        const y = canvas.height - 30 - (data.lufs[i] - minLufs + range/10) * lufsScale;
        
        if (i === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Draw Y-axis labels (LUFS)
    ctx.fillStyle = '#333';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    
    const numLabels = 5;
    for (let i = 0; i <= numLabels; i++) {
        const value = minLufs + (range * i / numLabels);
        const y = canvas.height - 30 - (value - minLufs) * lufsScale;
        ctx.fillText(value.toFixed(1) + ' dB', 35, y + 3);
    }
    
    // Draw X-axis labels (Time)
    ctx.textAlign = 'center';
    const maxTime = data.times[data.times.length - 1] || 0;
    const numTimeLabels = Math.min(5, maxTime);
    
    for (let i = 0; i <= numTimeLabels; i++) {
        const value = (maxTime * i / numTimeLabels);
        const x = 40 + value * timeScale;
        ctx.fillText(value.toFixed(0) + 's', x, canvas.height - 10);
    }
}

function showError(message) {
    console.error('Error:', message);
    alert('Error: ' + message);
}

function clearGraphs() {
    // Clear any existing charts
    if (window.lufsChart) {
        window.lufsChart.destroy();
        window.lufsChart = null;
    }
    
    // Clear any canvas elements
    const canvases = document.querySelectorAll('canvas');
    canvases.forEach(canvas => {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    });
}

// Simplified graph rendering function
function renderComparisonGraph(canvasElement, originalData, processedData, title) {
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }
    
    // Check if canvas element exists
    if (!canvasElement) {
        console.error('Canvas element not found');
        return;
    }
    
    // Check if we have data
    if (!originalData) {
        console.error('Original data is missing');
        return;
    }
    
    // Log data format for debugging
    console.log('Original data format:', originalData);
    if (processedData) {
        console.log('Processed data format:', processedData);
    }
    
    // Ensure data has the expected format
    if (!originalData.times || !originalData.lufs || !Array.isArray(originalData.times) || !Array.isArray(originalData.lufs)) {
        console.error('Original data has incorrect format');
        return;
    }
    
    // Get the 2D context
    const ctx = canvasElement.getContext('2d');
    
    // Destroy existing chart if it exists
    if (window.lufsChart) {
        window.lufsChart.destroy();
    }
    
    // Create datasets
    const datasets = [
        {
            label: 'Original',
            data: originalData.lufs.map((value, index) => ({
                x: originalData.times[index],
                y: value
            })),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            borderWidth: 2,
            tension: 0.1
        }
    ];
    
    // Add processed data if available
    if (processedData && processedData.times && processedData.lufs && 
        Array.isArray(processedData.times) && Array.isArray(processedData.lufs)) {
        datasets.push({
            label: 'Processed',
            data: processedData.lufs.map((value, index) => ({
                x: processedData.times[index],
                y: value
            })),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderWidth: 2,
            tension: 0.1
        });
    }
    
    // Create the chart
    window.lufsChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title || 'LUFS Comparison',
                    font: {
                        size: 16
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + ' LUFS';
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'linear',
                    title: {
                        display: true,
                        text: 'Time (seconds)'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'LUFS (dB)'
                    },
                    min: -45,
                    max: 0
                }
            }
        }
    });
    
    console.log('Chart rendered successfully');
}

// Helper function to format time in MM:SS.ms format
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toFixed(2).padStart(5, '0')}`;
}

// Function to add jingle detection results as a card in the main results area
function addJingleResultsCard(jingleMatches) {
    // Find the results container
    const resultsDiv = document.getElementById('results');
    if (!resultsDiv) {
        console.error('Results container not found');
        return;
    }

    // Remove any existing jingle card
    const existingJingleCard = document.getElementById('jingle-detection-card');
    if (existingJingleCard) {
        existingJingleCard.remove();
    }

    // Create the jingle detection card
    const jingleCard = document.createElement('div');
    jingleCard.id = 'jingle-detection-card';
    jingleCard.className = 'card mb-3';

    let cardContent = '';

    if (jingleMatches && jingleMatches.length > 0) {
        // Found jingles
        cardContent = `
            <div class="card-header">NRK Podcast Jingle Detection</div>
            <div class="card-body">
                <p class="mb-3">Found ${jingleMatches.length} jingle match${jingleMatches.length > 1 ? 'es' : ''}:</p>
                <ul class="list-group list-group-flush">
        `;

        jingleMatches.forEach((match, index) => {
            const startTime = formatTime(match.start);
            const endTime = formatTime(match.end);
            const confidence = (match.confidence * 100).toFixed(1);

            cardContent += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Match #${index + 1}</strong>
                        <div class="text-muted">Time: ${startTime} - ${endTime}</div>
                    </div>
                    <span class="badge bg-primary rounded-pill">Confidence: ${confidence}%</span>
                </li>
            `;
        });

        cardContent += `
                </ul>
            </div>
        `;
    } else {
        // No jingles found
        cardContent = `
            <div class="card-header">NRK Podcast Jingle Detection</div>
            <div class="card-body">
                <div class="alert alert-warning mb-0" role="alert">
                    <strong>Ingen jingle funnet.</strong><br>
                    Sjekk om din lydfil innholder rett lydprofil!
                </div>
            </div>
        `;
    }

    jingleCard.innerHTML = cardContent;

    // Insert the card after the LUFS values card but before the graph
    const lufsCard = resultsDiv.querySelector('.card');
    if (lufsCard && lufsCard.parentNode) {
        lufsCard.parentNode.insertBefore(jingleCard, lufsCard.nextSibling);
    } else {
        // Fallback: append to results div
        resultsDiv.appendChild(jingleCard);
    }
}

// Function to set up fixed compression settings (attack, release, ratio)
function setupFixedCompression() {
    const ratioInput = document.getElementById('ratio');
    const attackInput = document.getElementById('attack');
    const releaseInput = document.getElementById('release');

    if (!ratioInput || !attackInput || !releaseInput) {
        console.error('Compression input elements not found');
        return;
    }

    // Set fixed podcast-optimized compression settings
    const attack = 50;   // 50ms attack for natural speech
    const release = 100; // 100ms release for smooth compression
    const ratio = 2.8;   // 2.8:1 ratio for gentle but effective compression

    // Update hidden inputs
    ratioInput.value = ratio;
    attackInput.value = attack;
    releaseInput.value = release;

    console.log(`Fixed podcast compression settings: ratio=${ratio}:1, attack=${attack}ms, release=${release}ms`);
}

// Function to set up the LUFS round knob control
function setupLufsKnob() {
    const knob = document.getElementById('lufs-knob');
    const knobValue = document.getElementById('knob-value');
    const knobIndicator = knob.querySelector('.knob-indicator');
    const hiddenInput = document.getElementById('target_lufs');

    if (!knob || !knobValue || !knobIndicator || !hiddenInput) {
        console.error('Knob elements not found');
        return;
    }

    let isDragging = false;
    let startAngle = 0;
    let startValue = parseFloat(hiddenInput.value);

    // Convert LUFS value to rotation angle (-135° to +135°, total 270°)
    function valueToAngle(value) {
        const minValue = -30;
        const maxValue = -10;
        const minAngle = -135;
        const maxAngle = 135;

        const normalizedValue = (value - minValue) / (maxValue - minValue);
        return minAngle + (normalizedValue * (maxAngle - minAngle));
    }

    // Convert rotation angle to LUFS value
    function angleToValue(angle) {
        const minValue = -30;
        const maxValue = -10;
        const minAngle = -135;
        const maxAngle = 135;

        // Clamp angle to valid range
        angle = Math.max(minAngle, Math.min(maxAngle, angle));

        const normalizedAngle = (angle - minAngle) / (maxAngle - minAngle);
        return minValue + (normalizedAngle * (maxValue - minValue));
    }

    // Update knob visual state
    function updateKnob(value) {
        const angle = valueToAngle(value);
        knobIndicator.style.transform = `translateX(-50%) rotate(${angle}deg)`;
        knobValue.textContent = value.toFixed(1);
        hiddenInput.value = value;
    }

    // Get angle from mouse position relative to knob center
    function getAngleFromMouse(event, knobRect) {
        const centerX = knobRect.left + knobRect.width / 2;
        const centerY = knobRect.top + knobRect.height / 2;
        const deltaX = event.clientX - centerX;
        const deltaY = event.clientY - centerY;

        // Calculate angle in degrees, adjusted for our coordinate system
        let angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

        // Convert to our knob's coordinate system (0° at top, clockwise positive)
        angle = angle + 90;

        // Normalize to -180 to 180 range
        if (angle > 180) angle -= 360;
        if (angle < -180) angle += 360;

        return angle;
    }

    // Mouse down event
    function onMouseDown(event) {
        event.preventDefault();
        isDragging = true;

        const knobRect = knob.getBoundingClientRect();
        startAngle = getAngleFromMouse(event, knobRect);
        startValue = parseFloat(hiddenInput.value);

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);

        knob.style.cursor = 'grabbing';
    }

    // Mouse move event
    function onMouseMove(event) {
        if (!isDragging) return;

        const knobRect = knob.getBoundingClientRect();
        const currentAngle = getAngleFromMouse(event, knobRect);
        const angleDelta = currentAngle - startAngle;

        // Convert angle delta to value delta (more sensitive control)
        const valueDelta = (angleDelta / 270) * 20; // 270° = full range of 20 LUFS
        const newValue = startValue + valueDelta;

        // Clamp to valid range and round to nearest 0.5
        const clampedValue = Math.max(-30, Math.min(-10, newValue));
        const roundedValue = Math.round(clampedValue * 2) / 2;

        updateKnob(roundedValue);
    }

    // Mouse up event
    function onMouseUp() {
        isDragging = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        knob.style.cursor = 'pointer';
    }

    // Touch events for mobile support
    function onTouchStart(event) {
        event.preventDefault();
        const touch = event.touches[0];
        onMouseDown({
            clientX: touch.clientX,
            clientY: touch.clientY,
            preventDefault: () => {}
        });
    }

    function onTouchMove(event) {
        event.preventDefault();
        const touch = event.touches[0];
        onMouseMove({
            clientX: touch.clientX,
            clientY: touch.clientY
        });
    }

    function onTouchEnd(event) {
        event.preventDefault();
        onMouseUp();
    }

    // Add event listeners
    knob.addEventListener('mousedown', onMouseDown);
    knob.addEventListener('touchstart', onTouchStart);
    knob.addEventListener('touchmove', onTouchMove);
    knob.addEventListener('touchend', onTouchEnd);

    // Initialize knob position
    updateKnob(parseFloat(hiddenInput.value));
}

// Function to set up the threshold round knob control
function setupThresholdKnob() {
    const knob = document.getElementById('threshold-knob');
    const knobValue = document.getElementById('threshold-knob-value');
    const knobIndicator = knob.querySelector('.knob-indicator');
    const hiddenInput = document.getElementById('threshold');

    if (!knob || !knobValue || !knobIndicator || !hiddenInput) {
        console.error('Threshold knob elements not found');
        return;
    }

    let isDragging = false;
    let startAngle = 0;
    let startValue = parseFloat(hiddenInput.value);

    // Convert threshold value to rotation angle (-135° to +135°, total 270°)
    function valueToAngle(value) {
        const minValue = -20;
        const maxValue = -3;
        const minAngle = -135;
        const maxAngle = 135;

        const normalizedValue = (value - minValue) / (maxValue - minValue);
        return minAngle + (normalizedValue * (maxAngle - minAngle));
    }

    // Convert rotation angle to threshold value
    function angleToValue(angle) {
        const minValue = -20;
        const maxValue = -3;
        const minAngle = -135;
        const maxAngle = 135;

        // Clamp angle to valid range
        angle = Math.max(minAngle, Math.min(maxAngle, angle));

        const normalizedAngle = (angle - minAngle) / (maxAngle - minAngle);
        return minValue + (normalizedAngle * (maxValue - minValue));
    }

    // Update knob visual state
    function updateKnob(value) {
        const angle = valueToAngle(value);
        knobIndicator.style.transform = `translateX(-50%) rotate(${angle}deg)`;
        knobValue.textContent = value.toFixed(1);
        hiddenInput.value = value;

        console.log(`Threshold updated: ${value.toFixed(1)} dB`);
    }

    // Get angle from mouse position relative to knob center
    function getAngleFromMouse(event, knobRect) {
        const centerX = knobRect.left + knobRect.width / 2;
        const centerY = knobRect.top + knobRect.height / 2;
        const deltaX = event.clientX - centerX;
        const deltaY = event.clientY - centerY;

        // Calculate angle in degrees, adjusted for our coordinate system
        let angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

        // Convert to our knob's coordinate system (0° at top, clockwise positive)
        angle = angle + 90;

        // Normalize to -180 to 180 range
        if (angle > 180) angle -= 360;
        if (angle < -180) angle += 360;

        return angle;
    }

    // Mouse down event
    function onMouseDown(event) {
        event.preventDefault();
        isDragging = true;

        const knobRect = knob.getBoundingClientRect();
        startAngle = getAngleFromMouse(event, knobRect);
        startValue = parseFloat(hiddenInput.value);

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);

        knob.style.cursor = 'grabbing';
    }

    // Mouse move event
    function onMouseMove(event) {
        if (!isDragging) return;

        const knobRect = knob.getBoundingClientRect();
        const currentAngle = getAngleFromMouse(event, knobRect);
        const angleDelta = currentAngle - startAngle;

        // Convert angle delta to value delta (more sensitive control)
        const valueDelta = (angleDelta / 270) * 17; // 270° = full range of 17 dB (-20 to -3)
        const newValue = startValue + valueDelta;

        // Clamp to valid range and round to nearest 0.5
        const clampedValue = Math.max(-20, Math.min(-3, newValue));
        const roundedValue = Math.round(clampedValue * 2) / 2;

        updateKnob(roundedValue);
    }

    // Mouse up event
    function onMouseUp() {
        isDragging = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        knob.style.cursor = 'pointer';
    }

    // Touch events for mobile support
    function onTouchStart(event) {
        event.preventDefault();
        const touch = event.touches[0];
        onMouseDown({
            clientX: touch.clientX,
            clientY: touch.clientY,
            preventDefault: () => {}
        });
    }

    function onTouchMove(event) {
        event.preventDefault();
        const touch = event.touches[0];
        onMouseMove({
            clientX: touch.clientX,
            clientY: touch.clientY
        });
    }

    function onTouchEnd(event) {
        event.preventDefault();
        onMouseUp();
    }

    // Add event listeners
    knob.addEventListener('mousedown', onMouseDown);
    knob.addEventListener('touchstart', onTouchStart);
    knob.addEventListener('touchmove', onTouchMove);
    knob.addEventListener('touchend', onTouchEnd);

    // Initialize knob position
    updateKnob(parseFloat(hiddenInput.value));
}
