/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #eceef3;
  color: #333;
  line-height: 1.6;
  padding: 16px;
}

/* Container */
.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Card styling */
.card {
  background-color: rgb(249, 245, 245);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 24px;
}

/* Typography */
h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 24px;
  text-align: center;
}

h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

p {
  margin-bottom: 16px;
  color: #4a5568;
}

/* Form elements */
.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #4a5568;
}

input[type="file"] {
  width: 100%;
  padding: 10px;
  border: 2px solid #cbd5e0;
  border-radius: 8px;
  background-color: #f8fafc;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]:hover {
  border-color: #a0aec0;
  cursor: pointer;
}

input[type="number"] {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

input[type="number"]:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

/* Buttons */
#upload-button {
  display: inline-block;
  background-color: #4299e1;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  width: 100%;
  font-size: 16px;
}

.button:hover:not(:disabled) {
  background-color: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.button:active:not(:disabled) {
  transform: translateY(0);
}

/* Download button */
#download-link {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #38b2ac;
  color: white;
  font-weight: 600;
  padding: 14px 24px;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(56, 178, 172, 0.2);
}

#download-link:hover {
  background-color: #319795;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(56, 178, 172, 0.3);
}

/* Graph container */
.graph-container {
  width: 100%;
  height: 300px;
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
}

canvas {
  width: 100% !important;
  height: 100% !important;
}

/* Results section */
.result-item {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 24px;
}

.result-value {
  font-weight: 600;
  color: #2d3748;
}

/* Loading spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (min-width: 640px) {
  body {
    padding: 24px;
  }
  
  h1 {
    font-size: 32px;
  }
  
  .button, .download-button {
    width: auto;
  }
  
  .form-row {
    display: flex;
    gap: 16px;
  }
  
  .form-row .form-group {
    flex: 1;
  }
}

@media (min-width: 768px) {
  body {
    padding: 32px;
  }
  
  .container {
    padding: 32px;
  }
  
  .card, .result-item {
    padding: 32px;
  }
}

/* File upload styling */
.file-upload-container {
  position: relative;
  width: 100%;
  height: 120px;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #f8fafc;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-bottom: 20px;
}

.file-upload-container:hover {
  border-color: #4299e1;
  background-color: #ebf8ff;
}

.file-upload-container input[type="file"] {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-upload-icon {
  font-size: 24px;
  color: #4299e1;
  margin-bottom: 8px;
}

.file-upload-text {
  font-size: 14px;
  color: #718096;
}

.file-name {
  margin-top: 8px;
  font-size: 14px;
  color: #4a5568;
  word-break: break-all;
  max-width: 100%;
  text-align: center;
}

/* LUFS value display */
.lufs-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
}

.lufs-label {
  font-weight: 500;
  color: #4a5568;
}

.lufs-value {
  font-weight: 700;
  font-size: 18px;
  color: #2d3748;
}

/* Compression controls */
.compression-controls {
  background-color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
}

.compression-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #2d3748;
}

/* Range slider styling */
.form-control-range {
  width: 100%;
  display: block; /* Ensures it takes full width for calculations */
  margin-top: 5px; /* Space from main label if tooltip is above */
  margin-bottom: 5px; /* Space between slider and min/max labels */
}

/* Info box styling */
#compression-info {
  font-size: 14px;
  line-height: 1.5;
}

#compression-info ul {
  padding-left: 20px;
  margin-top: 10px;
}

#compression-info li {
  margin-bottom: 5px;
}

/* Info boxes */
.info-box {
    padding: 0.75rem;
    background-color: #eff6ff;
    color: #1e40af;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.info-box.hidden {
    display: none;
}

.info-button {
    background: none;
    border: none;
    color: #4299e1;
    cursor: pointer;
    padding: 0.25rem;
}

.info-button:hover {
    color: #3182ce;
}

/* Options containers */
.options-container {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: #f8fafc;
}

/* Slider styling */
.slider-container {
    margin-bottom: 12px;
}

.slider-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.slider-label span {
    font-size: 14px;
    color: #718096;
}

/* Styles for the old centered value display - no longer needed with tooltips */
/*
#attack-value, #release-value, #ratio-value, #threshold-value {
    font-weight: 600;
    font-size: 18px;
    color: #2d3748;
}
*/

.slider-wrapper {
  position: relative;
  /* Add padding-top if the main label is very close to where the tooltip appears */
  /* padding-top: 10px; */
}

.slider-value-tooltip {
  position: absolute;
  transform: translateX(-50%); /* Center the tooltip horizontally */
  background-color: #2d3748; /* Darker background for contrast */
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem; /* Approx 12.8px */
  white-space: nowrap; /* Prevent text wrapping */
  top: -30px; /* Position above the slider track. Adjust as needed. */
  pointer-events: none; /* So it doesn't interfere with slider interaction */
  opacity: 0; /* Initially hidden */
  transition: opacity 0.1s ease-in-out, left 0.05s linear; /* Combined transitions */
  z-index: 10; /* Ensure it's above other elements */
}

/* Show tooltip when the slider input is hovered (via wrapper), focused, or active */
.slider-wrapper:hover .slider-value-tooltip,
input[type="range"]:focus + .slider-value-tooltip,
input[type="range"]:active + .slider-value-tooltip {
  opacity: 1;
}
.error-box {
    color: #b91c1c;
}

/* Round Knob Styling - Waves OneKnob Style */
.knob-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.knob-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.knob {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
    box-shadow:
        8px 8px 16px #bebebe,
        -8px -8px 16px #ffffff,
        inset 2px 2px 4px rgba(0,0,0,0.1);
    position: relative;
    cursor: pointer;
    transition: all 0.1s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    user-select: none;
}

.knob:hover {
    box-shadow:
        10px 10px 20px #bebebe,
        -10px -10px 20px #ffffff,
        inset 2px 2px 4px rgba(0,0,0,0.15);
}

.knob:active {
    box-shadow:
        4px 4px 8px #bebebe,
        -4px -4px 8px #ffffff,
        inset 4px 4px 8px rgba(0,0,0,0.2);
}

.knob-indicator {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 25px;
    background: linear-gradient(180deg, #4CAF50, #45a049);
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transform-origin: 50% 52px; /* Distance from center of knob */
    transition: transform 0.1s ease;
}

.knob-value {
    font-size: 24px;
    font-weight: bold;
    color: #2d3748;
    margin-top: 8px;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.knob-unit {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    margin-top: -2px;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.knob-labels {
    display: flex;
    justify-content: space-between;
    width: 160px;
    font-size: 12px;
    color: #718096;
    text-align: center;
}

.knob-label-left, .knob-label-right {
    font-weight: 500;
}

.knob-label-left small, .knob-label-right small {
    font-size: 10px;
    color: #a0aec0;
    display: block;
    margin-top: 2px;
}

/* Knob rotation calculation:
   -30 LUFS = -135 degrees (bottom left)
   -10 LUFS = +135 degrees (bottom right)
   Total range: 270 degrees
*/

.success-box {
    background-color: #f0fdf4;
    border: 1px solid #dcfce7;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

/* Spinner */
.spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile-specific adjustments */
@media (max-width: 640px) {
    body {
        font-size: 14px;
    }
    
    h1 {
        font-size: 1.5rem;
    }
    
    .upload-button, .download-button {
        padding: 10px 16px;
    }
    
    .result-item, .upload-form {
        padding: 15px;
    }
}

/* Utility classes */
.hidden {
    display: none;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.text-red-500 {
    color: #ef4444;
}

.text-blue-600 {
    color: #2563eb;
}

.text-blue-500 {
    color: #3b82f6;
}

.text-blue-800 {
    color: #1e40af;
}

.text-green-500 {
    color: #10b981;
}

.text-gray-500 {
    color: #6b7280;
}

.text-gray-800 {
    color: #1f2937;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.text-sm {
    font-size: 0.875rem;
}

.text-xl {
    font-size: 1.25rem;
}

.bg-blue-50 {
    background-color: #eff6ff;
}

.bg-red-50 {
    background-color: #fef2f2;
}

.bg-green-50 {
    background-color: #f0fdf4;
}

.bg-gray-200 {
    background-color: #e5e7eb;
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.border {
    border-width: 1px;
}

.border-green-200 {
    border-color: #bbf7d0;
}

.hover\:underline:hover {
    text-decoration: underline;
}

.hover\:text-blue-700:hover {
    color: #1d4ed8;
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
}

.w-5, .h-5 {
    width: 1.25rem;
    height: 1.25rem;
}

.space-y-1 > * + * {
    margin-top: 0.25rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.list-disc {
    list-style-type: disc;
}

.pl-5 {
    padding-left: 1.25rem;
}
