// File input handling
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded");
    
    // Get all the elements
    const fileInput = document.getElementById('file');
    const fileNameDisplay = document.getElementById('file-name');
    const uploadButton = document.getElementById('upload-button');
    const uploadForm = document.getElementById('upload-form');
    const uploadError = document.getElementById('upload-error');
    const spinner = document.getElementById('spinner');
    
    // Compression elements
    const compressionCheckbox = document.getElementById('compression');
    const compressionOptions = document.getElementById('compression-options');
    const compressionInfoBtn = document.getElementById('compression-info-btn');
    const compressionInfo = document.getElementById('compression-info');
    
    // Debug element existence
    console.log("File input:", fileInput);
    console.log("File name display:", fileNameDisplay);
    console.log("Upload form:", uploadForm);
    console.log("Compression checkbox:", compressionCheckbox);
    console.log("Compression info button:", compressionInfoBtn);
    console.log("Compression info div:", compressionInfo);
    
    // Handle file input change
    if (fileInput && fileNameDisplay) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                fileNameDisplay.textContent = this.files[0].name;
            } else {
                fileNameDisplay.textContent = 'No file selected';
            }
        });
    }
    
    // Handle compression checkbox
    if (compressionCheckbox && compressionOptions) {
        compressionCheckbox.addEventListener('change', function() {
            console.log("Compression checkbox changed:", this.checked);
            compressionOptions.style.display = this.checked ? 'block' : 'none';
        });
    }
    
    // Handle compression info button
    if (compressionInfoBtn && compressionInfo) {
        compressionInfoBtn.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent any default button behavior
            console.log("Compression info button clicked");
            compressionInfo.classList.toggle('hidden');
        });
    }

    // Form submission handling
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(event) {
            event.preventDefault();
            console.log("Form submitted");

            // Get file input element directly
            const fileInput = document.getElementById('file');
            
            if (!fileInput || !fileInput.files || !fileInput.files[0]) {
                console.error("No file selected");
                alert('Please select an audio file.');
                return;
            }

            const file = fileInput.files[0];
            console.log("Selected file:", file);
            
            const maxLufs = document.getElementById('target_lufs')?.value;
            console.log("Target LUFS:", maxLufs);

            if (!maxLufs) {
                console.error("No LUFS value entered");
                alert('Please enter a target LUFS value.');
                return;
            }

            // Disable button and show spinner
            if (uploadButton) {
                uploadButton.disabled = true;
                document.getElementById('button-text').textContent = 'Processing...';
            }
            if (spinner) spinner.style.display = 'inline-flex';
            
            // Clear previous graphs
            clearGraphs();
            
            // Clear previous error messages
            if (uploadError) uploadError.textContent = '';

            const formData = new FormData();
            formData.append('file', file);
            formData.append('max_lufs', maxLufs);
            formData.append('normalize', 'true'); // Ensure normalization is requested
            
            // Add compression parameters if enabled
            if (compressionCheckbox && compressionCheckbox.checked) {
                formData.append('compression', 'true');
                formData.append('threshold', document.getElementById('threshold')?.value || '-20');
                formData.append('ratio', document.getElementById('ratio')?.value || '4');
                formData.append('attack', document.getElementById('attack')?.value || '3.1');
                formData.append('release', document.getElementById('release')?.value || '100');
            }

            // Use absolute URL to ensure consistency
            const endpoint = window.location.origin + '/';
            console.log("Sending request to:", endpoint);

            fetch(endpoint, {
                method: 'POST',
                body: formData,
                cache: 'no-store' // Prevent caching issues
            })
            .then(response => {
                console.log("Response status:", response.status);
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Response data:", data);
                
                if (data.error) {
                    console.error("Server error:", data.error);
                    if (uploadError) uploadError.textContent = data.error;
                    
                    // Re-enable button even on error
                    if (uploadButton) {
                        uploadButton.disabled = false;
                        document.getElementById('button-text').textContent = 'Analyze Audio';
                    }
                    if (spinner) spinner.style.display = 'none';
                    return;
                }
                
                // Re-enable button
                if (uploadButton) {
                    uploadButton.disabled = false;
                    document.getElementById('button-text').textContent = 'Analyze Audio';
                }
                if (spinner) spinner.style.display = 'none';
                
                // Display results
                const resultsDiv = document.getElementById('results');
                if (resultsDiv) {
                    resultsDiv.style.display = 'block';
                    
                    // Update LUFS values
                    const lufsValue = document.getElementById('lufs-value');
                    if (lufsValue) lufsValue.textContent = data.lufs ? data.lufs.toFixed(1) + ' LUFS' : 'N/A';
                    
                    const processedLufsValue = document.getElementById('processed-lufs-value');
                    if (processedLufsValue) {
                        if (data.processed_lufs) {
                            processedLufsValue.textContent = data.processed_lufs.toFixed(1) + ' LUFS';
                            document.getElementById('processed-lufs-container').style.display = 'block';
                        } else {
                            document.getElementById('processed-lufs-container').style.display = 'none';
                        }
                    }
                    
                    // Show download link if processed file exists
                    const downloadLink = document.getElementById('download-link');
                    if (downloadLink) {
                        if (data.processed_file) {
                            // Use absolute URL path to ensure consistency
                            downloadLink.href = window.location.origin + '/uploads/' + data.processed_file;
                            downloadLink.style.display = 'inline-block';
                            console.log("Download link set to:", downloadLink.href);
                        } else {
                            downloadLink.style.display = 'none';
                            console.log("No processed file available, hiding download link");
                        }
                    }
                    
                    // Render comparison graph if available
                    console.log("Original graph data:", data.original_graph_data);
                    console.log("Processed graph data:", data.processed_graph_data);
                    
                    const comparisonCanvas = document.getElementById('comparison-graph');
                    if (comparisonCanvas && data.original_graph_data) {
                        renderComparisonGraph(
                            comparisonCanvas,
                            data.original_graph_data,
                            data.processed_graph_data,
                            'LUFS Comparison'
                        );
                    }
                }
            })
            .catch(error => {
                console.error("Fetch error:", error);
                if (uploadError) uploadError.textContent = 'An unexpected error occurred: ' + error.message;
                if (uploadButton) {
                    uploadButton.disabled = false;
                    document.getElementById('button-text').textContent = 'Analyze Audio';
                }
                if (spinner) spinner.style.display = 'none';
            });
        });
    }

    // Function to clear existing graphs
    function clearGraphs() {
        const originalCanvas = document.getElementById('original-graph');
        const processedCanvas = document.getElementById('processed-graph');
        
        if (originalCanvas && originalCanvas.chart) {
            originalCanvas.chart.destroy();
        }
        
        if (processedCanvas && processedCanvas.chart) {
            processedCanvas.chart.destroy();
        }
    }
    
    // Graph rendering function
    function renderGraph(canvasElement, data, title) {
        if (!canvasElement || !data || !data.time || !data.loudness) {
            console.error('Missing data for graph rendering', {
                canvasElement: !!canvasElement,
                data: !!data,
                time: data?.time ? 'present' : 'missing',
                loudness: data?.loudness ? 'present' : 'missing'
            });
            return;
        }
        
        const ctx = canvasElement.getContext('2d');
        if (!ctx) {
            console.error('Failed to get canvas context');
            return;
        }

        // Destroy existing chart if it exists
        if (canvasElement.chart) {
            canvasElement.chart.destroy();
        }
        
        console.log(`Rendering ${title} graph with ${data.time.length} data points`);
        console.log("Time values:", data.time.slice(0, 5), "...");
        console.log("Loudness values:", data.loudness.slice(0, 5), "...");

        // Create new chart
        canvasElement.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.time.map(t => t.toFixed(1) + 's'),
                datasets: [{
                    label: 'LUFS',
                    data: data.loudness,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.raw.toFixed(1) + ' LUFS';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        min: Math.min(-70, data.min - 5),
                        max: Math.max(-10, data.max + 5),
                        title: {
                            display: true,
                            text: 'LUFS'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time (seconds)'
                        }
                    }
                }
            }
        });
    }

    // Graph rendering function for comparing original and processed data
    function renderComparisonGraph(canvasElement, originalData, processedData, title) {
        if (!canvasElement || !originalData || !originalData.time || !originalData.loudness) {
            console.error('Missing data for graph rendering', {
                canvasElement: !!canvasElement,
                originalData: !!originalData,
                time: originalData?.time ? 'present' : 'missing',
                loudness: originalData?.loudness ? 'present' : 'missing'
            });
            return;
        }
        
        const ctx = canvasElement.getContext('2d');
        if (!ctx) {
            console.error('Failed to get canvas context');
            return;
        }

        // Destroy existing chart if it exists
        if (canvasElement.chart) {
            canvasElement.chart.destroy();
        }
        
        // Prepare datasets
        const datasets = [
            {
                label: 'Original',
                data: originalData.loudness,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: false
            }
        ];
        
        // Add processed data if available
        if (processedData && processedData.time && processedData.loudness) {
            datasets.push({
                label: 'Processed',
                data: processedData.loudness,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1,
                fill: false
            });
        }
        
        // Find min and max values across both datasets
        let allLoudness = [...originalData.loudness];
        if (processedData && processedData.loudness) {
            allLoudness = [...allLoudness, ...processedData.loudness];
        }
        const minLoudness = Math.min(...allLoudness);
        const maxLoudness = Math.max(...allLoudness);

        // Create new chart
        canvasElement.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: originalData.time.map(t => t.toFixed(1) + 's'),
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: title || 'LUFS Comparison'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.raw.toFixed(1) + ' LUFS';
                            }
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        min: Math.min(-70, minLoudness - 5),
                        max: Math.max(-10, maxLoudness + 5),
                        title: {
                            display: true,
                            text: 'LUFS'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time (seconds)'
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }
});

// Global variables for tracking task status
let currentTaskId = null;
const progressContainer = document.getElementById('progress-container');
const progressBar = document.getElementById('progress-bar');
const statusMessage = document.getElementById('status-message');
const resultsDiv = document.getElementById('results');

// Form submission handler
document.getElementById('audio-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show progress container
    progressContainer.style.display = 'block';
    progressBar.style.width = '0%';
    progressBar.textContent = '0%';
    statusMessage.textContent = 'Uploading file...';
    
    // Hide results if they were previously shown
    if (resultsDiv) {
        resultsDiv.style.display = 'none';
    }
    
    const formData = new FormData(this);
    
    // Submit the form
    fetch('/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Form submission response:', data);
        
        if (data.error) {
            showError(data.error);
            progressContainer.style.display = 'none';
            return;
        }
        
        // Store the task ID and start polling
        currentTaskId = data.task_id;
        statusMessage.textContent = 'Processing audio...';
        pollTaskStatus();
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        showError('Error submitting form: ' + error);
        progressContainer.style.display = 'none';
    });
});

function pollTaskStatus() {
    if (!currentTaskId) {
        console.error('No task ID available for polling');
        return;
    }
    
    console.log('Polling task status for:', currentTaskId);
    
    fetch(`/task/${currentTaskId}`)
        .then(response => response.json())
        .then(data => {
            console.log('Task status update:', data);
            
            if (data.error) {
                showError(data.error);
                progressContainer.style.display = 'none';
                return;
            }
            
            // Update progress bar
            const progressPercent = Math.round(data.progress * 100);
            progressBar.style.width = `${progressPercent}%`;
            progressBar.textContent = `${progressPercent}%`;
            
            // Update status message
            switch (data.status) {
                case 'pending':
                    statusMessage.textContent = 'Waiting in queue...';
                    break;
                case 'processing':
                    statusMessage.textContent = 'Processing audio...';
                    break;
                case 'completed':
                    statusMessage.textContent = 'Processing complete!';
                    // Display results
                    displayResults(data.result);
                    return; // Stop polling
                case 'failed':
                    showError('Processing failed: ' + data.error);
                    progressContainer.style.display = 'none';
                    return; // Stop polling
                default:
                    statusMessage.textContent = `Status: ${data.status}`;
            }
            
            // Continue polling
            setTimeout(pollTaskStatus, 1000);
        })
        .catch(error => {
            console.error('Error checking task status:', error);
            showError('Error checking task status: ' + error);
            setTimeout(pollTaskStatus, 2000); // Retry with longer delay
        });
}

function displayResults(data) {
    console.log('Displaying results:', data);
    
    if (!data) {
        console.error('No result data to display');
        return;
    }
    
    // Hide progress container
    progressContainer.style.display = 'none';
    
    // Show results container
    if (resultsDiv) {
        resultsDiv.style.display = 'block';
    }
    
    // Update LUFS values
    const lufsValue = document.getElementById('lufs-value');
    if (lufsValue && data.lufs !== undefined) {
        lufsValue.textContent = data.lufs.toFixed(1) + ' LUFS';
    }
    
    const processedLufsValue = document.getElementById('processed-lufs-value');
    const processedLufsContainer = document.getElementById('processed-lufs-container');
    
    if (processedLufsValue && processedLufsContainer) {
        if (data.processed_lufs !== undefined) {
            processedLufsValue.textContent = data.processed_lufs.toFixed(1) + ' LUFS';
            processedLufsContainer.style.display = 'block';
        } else {
            processedLufsContainer.style.display = 'none';
        }
    }
    
    // Update download link
    const downloadLink = document.getElementById('download-link');
    if (downloadLink) {
        if (data.processed_file) {
            downloadLink.href = `/uploads/${data.processed_file}`;
            downloadLink.style.display = 'inline-block';
            console.log('Download link set to:', downloadLink.href);
        } else {
            downloadLink.style.display = 'none';
            console.log('No processed file available, hiding download link');
        }
    }
    
    // Render comparison graph
    const comparisonCanvas = document.getElementById('comparison-graph');
    if (comparisonCanvas && data.original_graph_data) {
        renderComparisonGraph(
            comparisonCanvas,
            data.original_graph_data,
            data.processed_graph_data,
            'LUFS Comparison'
        );
    }
}

function showError(message) {
    console.error('Error:', message);
    alert('Error: ' + message);
}

// Add this function if you don't have a graph drawing function
function drawLufsGraph(containerId, data) {
    if (!data || !data.times || !data.lufs || data.times.length === 0) {
        console.error('Invalid graph data for', containerId);
        return;
    }
    
    console.log(`Drawing graph in ${containerId} with ${data.times.length} data points`);
    
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('Graph container not found:', containerId);
        return;
    }
    
    // Clear previous content
    container.innerHTML = '';
    
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.width = container.clientWidth || 400;
    canvas.height = 200;
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // Set up the graph
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Find min and max values for scaling
    const minLufs = Math.min(...data.lufs);
    const maxLufs = Math.max(...data.lufs);
    const range = Math.max(Math.abs(maxLufs - minLufs), 10); // Ensure at least 10dB range
    
    // Draw axes
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(40, 10);
    ctx.lineTo(40, canvas.height - 30);
    ctx.lineTo(canvas.width - 10, canvas.height - 30);
    ctx.stroke();
    
    // Draw LUFS line
    ctx.strokeStyle = '#4CAF50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const timeScale = (canvas.width - 50) / (data.times[data.times.length - 1] || 1);
    const lufsScale = (canvas.height - 40) / range;
    
    for (let i = 0; i < data.times.length; i++) {
        const x = 40 + data.times[i] * timeScale;
        // Invert Y axis since canvas 0 is at top
        const y = canvas.height - 30 - (data.lufs[i] - minLufs + range/10) * lufsScale;
        
        if (i === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Draw Y-axis labels (LUFS)
    ctx.fillStyle = '#333';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    
    const numLabels = 5;
    for (let i = 0; i <= numLabels; i++) {
        const value = minLufs + (range * i / numLabels);
        const y = canvas.height - 30 - (value - minLufs) * lufsScale;
        ctx.fillText(value.toFixed(1) + ' dB', 35, y + 3);
    }
    
    // Draw X-axis labels (Time)
    ctx.textAlign = 'center';
    const maxTime = data.times[data.times.length - 1] || 0;
    const numTimeLabels = Math.min(5, maxTime);
    
    for (let i = 0; i <= numTimeLabels; i++) {
        const value = (maxTime * i / numTimeLabels);
        const x = 40 + value * timeScale;
        ctx.fillText(value.toFixed(0) + 's', x, canvas.height - 10);
    }
}
