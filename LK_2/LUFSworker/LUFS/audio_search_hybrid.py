#!/usr/bin/env python3
"""
Hybrid audio search using both correlation and spectral analysis
"""
import numpy as np
import librosa
import soundfile as sf
from scipy.signal import correlate
import logging
import os

# Set up logging
logger = logging.getLogger(__name__)

def find_jingle_occurrences_hybrid(audio_data, sample_rate, jingle_path, threshold=0.75):
    """
    Find jingle occurrences using hybrid approach:
    1. Fast correlation-based pre-filtering
    2. Spectral analysis for verification
    """
    
    try:
        logger.info(f"Starting hybrid jingle detection with threshold={threshold}")
        
        # Convert main audio to mono if stereo
        if len(audio_data.shape) > 1:
            logger.info(f"Converting stereo audio to mono, original shape: {audio_data.shape}")
            if audio_data.shape[0] == 2 and audio_data.shape[1] > audio_data.shape[0]:
                audio_mono = np.mean(audio_data, axis=0)
            elif audio_data.shape[1] == 2 and audio_data.shape[0] > audio_data.shape[1]:
                audio_mono = np.mean(audio_data, axis=1)
            else:
                audio_mono = np.mean(audio_data, axis=-1)
        else:
            audio_mono = audio_data
        
        # Load jingle file
        y_jingle, sr_jingle = librosa.load(jingle_path, sr=None, mono=True)
        
        # Resample jingle if needed
        if sr_jingle != sample_rate:
            logger.info(f"Resampling jingle from {sr_jingle}Hz to {sample_rate}Hz")
            y_jingle = librosa.resample(y_jingle, orig_sr=sr_jingle, target_sr=sample_rate)
        
        logger.info(f"Audio durations: main={len(audio_mono)/sample_rate:.2f}s, jingle={len(y_jingle)/sample_rate:.2f}s")
        
        # Step 1: Fast correlation-based pre-filtering
        logger.info("Step 1: Correlation-based pre-filtering")
        correlation_candidates = correlation_prefilter(audio_mono, y_jingle, sample_rate, threshold=0.1)
        
        if not correlation_candidates:
            logger.info("No correlation candidates found")
            return []
        
        logger.info(f"Found {len(correlation_candidates)} correlation candidates")
        
        # Step 2: Spectral verification of candidates
        logger.info("Step 2: Spectral verification")
        verified_matches = []
        
        jingle_features = extract_spectral_features(y_jingle, sample_rate)
        
        for candidate in correlation_candidates:
            start_sample = int(candidate['start'] * sample_rate)
            end_sample = int(candidate['end'] * sample_rate)
            
            # Extract candidate audio segment
            if end_sample <= len(audio_mono):
                candidate_audio = audio_mono[start_sample:end_sample]
                
                # Extract spectral features
                candidate_features = extract_spectral_features(candidate_audio, sample_rate)
                
                # Calculate spectral similarity
                spectral_similarity = calculate_spectral_similarity(candidate_features, jingle_features)
                
                # Combine correlation and spectral scores
                combined_score = (candidate['confidence'] * 0.4) + (spectral_similarity * 0.6)
                
                if combined_score >= threshold:
                    verified_matches.append({
                        "start": candidate['start'],
                        "end": candidate['end'],
                        "confidence": float(combined_score)
                    })
                    
                    logger.info(f"Verified match: {candidate['start']:.2f}s, combined={combined_score:.3f}")
        
        # Sort by confidence and return top matches
        verified_matches.sort(key=lambda x: x['confidence'], reverse=True)
        return verified_matches[:5]
        
    except Exception as e:
        logger.error(f"Error in hybrid jingle detection: {e}", exc_info=True)
        return []

def correlation_prefilter(audio_mono, y_jingle, sample_rate, threshold=0.1):
    """Fast correlation-based pre-filtering to find potential candidates"""
    
    # Normalize audio for correlation
    y_main_norm = (audio_mono - np.mean(audio_mono)) / (np.std(audio_mono) + 1e-10)
    y_jingle_norm = (y_jingle - np.mean(y_jingle)) / (np.std(y_jingle) + 1e-10)
    
    # Perform cross-correlation
    correlation = correlate(y_main_norm, y_jingle_norm, mode='valid', method='fft')
    
    if len(correlation) == 0:
        return []
    elif len(correlation) == 1:
        # Handle case where audio files are the same length
        # Calculate direct correlation for normalization
        direct_corr = np.corrcoef(y_main_norm, y_jingle_norm)[0, 1]
        if not np.isnan(direct_corr) and direct_corr > threshold:
            jingle_duration = len(y_jingle) / sample_rate
            return [{
                "start": 0.0,
                "end": float(jingle_duration),
                "confidence": float(direct_corr)
            }]
        return []
    
    # Normalize correlation
    correlation_normalized = (correlation - np.min(correlation)) / (np.max(correlation) - np.min(correlation) + 1e-10)
    
    # Find peaks above threshold
    candidates = []
    jingle_duration = len(y_jingle) / sample_rate
    
    # Simple peak detection with minimum distance
    min_distance_samples = int(0.5 * sample_rate)  # 0.5 second minimum distance
    
    for i in range(len(correlation_normalized)):
        if correlation_normalized[i] > threshold:
            # Check if this is a local maximum
            window = min(min_distance_samples, len(correlation_normalized) // 10)
            start_idx = max(0, i - window)
            end_idx = min(len(correlation_normalized), i + window)
            
            is_peak = True
            for j in range(start_idx, end_idx):
                if j != i and correlation_normalized[j] > correlation_normalized[i]:
                    is_peak = False
                    break
            
            if is_peak:
                start_time = i / sample_rate
                end_time = start_time + jingle_duration
                
                # Check for overlap with existing candidates
                overlap = False
                for existing in candidates:
                    if abs(start_time - existing['start']) < jingle_duration * 0.5:
                        overlap = True
                        # Keep the one with higher confidence
                        if correlation_normalized[i] > existing['confidence']:
                            existing['start'] = start_time
                            existing['end'] = end_time
                            existing['confidence'] = float(correlation_normalized[i])
                        break
                
                if not overlap:
                    candidates.append({
                        "start": float(start_time),
                        "end": float(end_time),
                        "confidence": float(correlation_normalized[i])
                    })
    
    return candidates

def extract_spectral_features(audio, sample_rate):
    """Extract spectral features for audio matching"""
    # Extract MFCC features
    mfccs = librosa.feature.mfcc(y=audio, sr=sample_rate, n_mfcc=13)
    
    # Extract spectral centroid
    spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sample_rate)
    
    return {
        'mfcc': mfccs,
        'spectral_centroid': spectral_centroids
    }

def calculate_spectral_similarity(features1, features2):
    """Calculate similarity between two sets of spectral features"""
    similarities = []
    
    # MFCC similarity (most important)
    mfcc_corr = np.corrcoef(features1['mfcc'].flatten(), features2['mfcc'].flatten())[0, 1]
    if not np.isnan(mfcc_corr):
        similarities.append(mfcc_corr)
    
    # Spectral centroid similarity
    centroid_corr = np.corrcoef(features1['spectral_centroid'].flatten(), features2['spectral_centroid'].flatten())[0, 1]
    if not np.isnan(centroid_corr):
        similarities.append(centroid_corr)
    
    # Return average similarity
    if similarities:
        return max(0.0, min(1.0, float(np.mean(similarities))))
    else:
        return 0.0
