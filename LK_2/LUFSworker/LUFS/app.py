import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

from flask import Flask, request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename
import os
import uuid

# Import audio utilities
from audio_utils import (
    calculate_lufs, create_lufs_graph, normalize_audio_loudness,
    apply_compression, is_stereo
)

app = Flask(__name__)
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
ALLOWED_EXTENSIONS = {'wav', 'mp3', 'ogg', 'flac', 'aiff', 'm4a'}

# Make sure upload directory exists
upload_path = os.path.abspath(UPLOAD_FOLDER)
os.makedirs(upload_path, exist_ok=True)
logger.info(f"Upload directory: {upload_path}")

# Initialize the audio processor
from worker import AudioProcessor
audio_processor = AudioProcessor(UPLOAD_FOLDER)


def allowed_file(filename):
    """Check if a filename has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        try:
            # Check if the post request has the file part
            if 'file' not in request.files:
                return jsonify({'error': 'No file part in the request'}), 400
            
            file = request.files['file']
            
            # If user does not select file, browser also
            # submit an empty part without filename
            if file.filename == '':
                return jsonify({'error': 'No selected file'}), 400
            
            # Check if the file is allowed
            if not allowed_file(file.filename):
                return jsonify({'error': 'File type not allowed. Please upload a WAV, MP3, OGG, FLAC, AIFF, or M4A file.'}), 400
            
            # Save the file
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Extract parameters from the request
            params = {
                'max_lufs': request.form.get('max_lufs'),
                'normalize': request.form.get('normalize', 'true'),
                'compression': request.form.get('compression'),
                'threshold': request.form.get('threshold', '-20'),
                'ratio': request.form.get('ratio', '4'),
                'attack': request.form.get('attack', '3.1'),
                'release': request.form.get('release', '100'),
                'nrk_podcast_check': request.form.get('nrk_podcast_check')
            }
            
            # Log parameters for debugging
            app.logger.info(f"Processing parameters: {params}")
            
            # Add task to processor queue
            task_id = audio_processor.add_task(filepath, params)
            
            return jsonify({
                'task_id': task_id,
                'message': 'File uploaded and processing started'
            })
            
        except Exception as e:
            app.logger.error(f"Error processing upload: {str(e)}", exc_info=True)
            return jsonify({'error': f'Error processing upload: {str(e)}'}), 500
    
    return render_template('index.html')

@app.route('/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Get the status of a task"""
    logger.info(f"Getting status for task: {task_id}")
    task = audio_processor.get_task_status(task_id)
    if task:
        logger.info(f"Task status: {task}")
        return jsonify(task)
    logger.error(f"Task not found: {task_id}")
    return jsonify({'error': 'Task not found'}), 404

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    logger.info(f"Download request for filename: '{filename}' from folder '{app.config['UPLOAD_FOLDER']}'")
    
    try:
        # Make sure the file exists
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.isfile(file_path):
            logger.error(f"File not found: {file_path}")
            return jsonify({"error": "File not found"}), 404
            
        # Use absolute path to avoid routing issues
        return send_from_directory(
            os.path.abspath(app.config['UPLOAD_FOLDER']),
            filename,
            as_attachment=True
        )
    except Exception as e:
        logger.error(f"Error sending file '{filename}': {e}", exc_info=True)
        return jsonify({"error": f"Server error while sending file: {str(e)}"}), 500


if __name__ == '__main__':
    app.run(debug=True, port=5002)
