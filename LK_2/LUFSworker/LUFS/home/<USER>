document.addEventListener('DOMContentLoaded', () => {

    // Register ScrollTrigger with GSAP
    gsap.registerPlugin(ScrollTrigger);
    // --- Header Scroll Hide/Show ---
    let lastScrollTop = 0;
    const header = document.querySelector('.site-header');
    const headerHeight = header.offsetHeight; // Get header height for threshold

    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (scrollTop > lastScrollTop && scrollTop > headerHeight) {
            // Scrolling Down
            header.classList.add('header-hidden');
        } else {
            // Scrolling Up or at the top
            header.classList.remove('header-hidden');
        }
        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop; // For Mobile or negative scrolling
    }, false);

    // --- Hamburger Menu Toggle ---
    const hamburgerButton = document.querySelector('.hamburger-menu');
    const mainNav = document.querySelector('.main-nav');

    hamburgerButton.addEventListener('click', () => {
        mainNav.classList.toggle('nav-active');
        hamburgerButton.classList.toggle('active'); // For animating hamburger to X
        const isExpanded = mainNav.classList.contains('nav-active');
        hamburgerButton.setAttribute('aria-expanded', isExpanded);
    });
    // --- Hero Section Parallax Effect ---
    gsap.to(".hero", {
        scrollTrigger: {
            trigger: ".section-features",
            start: "top bottom", 
            end: "top top", 
            scrub: true,
        },
        scale: 1.2,
        opacity: 0.7,
        ease: "none"
    });
    gsap.to(".features-left-column", 
        {
            backgroundPosition: "100% 80%",
            ease: "none",
            scrollTrigger: {
                trigger: ".hero",
                start: "bottom bottom",
                end: "bottom top",
                scrub: true,
            }
        }
    );
    gsap.to(".learnmore-left-column", 
        {
            backgroundPosition: "100% 80%",
            ease: "none",
            scrollTrigger: {
                trigger: ".features-left-column",
                start: "bottom bottom",
                end: "bottom top",
                scrub: true,
            }
        }
    );
    // --- Section Filler Background Side Scroll ---
    gsap.to(".section-filler", 
        {
            backgroundPosition: "100% 10%",
            ease: "none",
            scrollTrigger: {
                trigger: ".lastopp",
                start: "top bottom",
                end: "bottom",
                scrub: true,
            }
        }
    );
    
    gsap.to(".lastopp", 
        {
            backgroundPosition: "100% 80%",
            ease: "none",
            scrollTrigger: {
                trigger: ".footer",
                start: "top bottom",
                end: "bottom top",
                scrub: true,
            }
        }
    );
  

   
});
