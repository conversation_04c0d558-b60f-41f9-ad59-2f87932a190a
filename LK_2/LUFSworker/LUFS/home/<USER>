body, html {
    margin: 0;
    padding: 0;
    font-family: sans-serif;
    background-color: #141416;
    overflow-x: hidden;
    
    /* scroll-behavior: smooth; /* For smooth scrolling on anchor links, apply here */
    
}

/* Adjust body to account for fixed header height - adjust 70px if your header height changes */
body {
    padding-top: 70px; 
}

.hero {
    background-image: url('./images/hero.jpg'); 
    background-size: cover; /* Cover the entire section */
    background-position: center; /* Center the image */
    background-repeat: no-repeat; /* Do not repeat the image */
    min-height: 100vh; /* Make the section at least the height of the viewport */
    background-attachment: fixed;
    /* background-repeat: no-repeat; /* This was redundant */
    position: relative; /* Establish stacking context */
    z-index: 1;         /* Base stacking order */
    /* transform: scale(1); /* GSAP will handle scaling */
    /* scroll-behavior: smooth; /* Better on html/body for anchor links */
    overflow: hidden; /* Important: Prevents scrollbars when scaled */
}
.hero-button {
    background-color: #b73a3a;
    border: none;
    border-radius: 5px;
    color:black;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin-left: 60px;
    margin-top: 40px;
    transition: background-color 0.3s ease;
    transition: left ease-in 1.4s;
}

.hero-button:hover {
    background-color: #000000;
    border: 1px solid white;
    color: white;
}
.hero h1 {
    color: white;
    font-size: 80px;
    font-weight: lighter;
    text-align: left;
    padding-top: 90px;
    padding-left: 60px;
    transition: left ease-in 1s;
}
.hero h2 {
    color: white;
    font-size: 40px;
    font-weight: lighter;
    text-align: left;
    padding-left: 60px;
    transition: left ease-in 1.2s;
}
/*
h1 {
    color: white;
    font-size: 80px;
    font-weight: lighter;
    text-align: left;
    padding-top: 90px;
    padding-left: 60px;
    transition: left ease-in 1s;
}
h2 {
    color: white;
    font-size: 40px;
    font-weight: lighter;
    text-align: left;
    padding-left: 60px;
    transition: left ease-in 1.2s;
}
h3 {
    color: rgb(27, 116, 199);
    font-size: 20px;
    font-weight: lighter;
    text-align: left;
    padding-left: 60px;
    transition: left ease-in 1.4s;
}
h4 {
    color: rgb(220, 31, 31);
    font-size: 45px;
    font-weight: lighter;
    text-align: left;
    padding-left: 60px;
    transition: left ease-in 1.4s;
}
p {
    color: rgb(231, 222, 222);
    font-size: 18px;
    font-weight: lighter;
    text-align: left;
    padding-left: 60px;
    transition: left ease-in 1.4s;
}
*/
/* Header Styles */
.site-header {
    background-color: #131517; 
    color: white;
    padding: 20px 0;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000; /* Ensures header stays on top */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for depth */
    font-weight: lighter;
    transition: top 0.3s ease-in-out; /* For smooth hide/show on scroll */
}

.site-header.header-hidden {
    top: -75px; /* Adjust this value to be a bit more than your header's actual height */
}

.header-container {
    width: 90%;
    max-width: 1200px; /* Max width for content within header - increased slightly */
    margin: 0 auto; /* Center the container */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-header .logo {
    font-size: 28px;
    font-weight: bold;
    color: white;
    text-decoration: none;
    display: flex; /* Added to align image and text */
    align-items: center; /* Added for vertical alignment */
}

.hamburger-menu {
    display: none; /* Hidden by default, shown on mobile */
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001; /* Ensure it's above nav items if they overlap */
}

.hamburger-menu span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 5px 0;
    transition: all 0.3s ease-in-out;
}

.main-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.main-nav li {
    margin-left: 25px; /* Spacing between nav items */
}

.main-nav a {
    color: white;
    text-decoration: none;
    font-size: 18px;
    padding: 5px 0;;
    transition: transform 0.3s ease;
}

.main-nav a:hover {
    text-decoration: underline;
    
}

/* Responsive Styles for Header and Navigation */
@media (max-width: 768px) {
    .header-container {
        /* Adjust if logo needs different alignment when nav is toggled */
    }

    .main-nav ul {
        display: none; /* Hide desktop nav by default on mobile */
        flex-direction: column;
        position: absolute;
        top: 100%; /* Position below the header (relative to .site-header) */
        left: 0;
        width: 100%;
        background-color: #131517; /* Match header background */
        padding: 10px 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1); /* Shadow for dropdown effect */
    }

    .main-nav.nav-active ul {
        display: flex; /* Show when active */
    }

    .main-nav li {
        margin-left: 0;
        text-align: center;
        width: 100%;
    }

    .main-nav a {
        display: block; /* Make links take full width for easier tapping */
        padding: 15px;
        margin-bottom: 20px;
    }

    .hamburger-menu {
        display: block; /* Show hamburger icon on mobile */
        margin-left: auto;
    }

    /* Hamburger animation to "X" when active */
    .hamburger-menu.active span:nth-child(1) { transform: translateY(8px) rotate(45deg); }
    .hamburger-menu.active span:nth-child(2) { opacity: 0; }
    .hamburger-menu.active span:nth-child(3) { transform: translateY(-8px) rotate(-45deg); }
}


.section-features {
    /* You might want to add some padding to the section itself */
    
    min-height: 100vh;
    width: 90%;
    margin: 0 auto;
    position: relative; /* Establish stacking context */
    z-index: 2;         /* Ensure it's above the hero section */
    background-color: #141316; /* Overall section background */
    
 
}

.features-content-wrapper {
    display: flex;
    padding-bottom: 80px;
    align-items: center; /* Vertically aligns content if boxes have different heights */
    margin: 0 auto; /* Centers the wrapper */ 
}

.features-left-column {
    flex: 1; /* Takes up 1 part of the available space */
    min-height: 824px;
    background-image: url('./images/section1.jpg');
    background-size: cover; /* Cover the entire section */
    background-position: center; /* Center the image */
    background-repeat: no-repeat; /* Do not repeat the image */
    display: flex; /* Enable flex for content centering */
    flex-direction: column; /* Stack content vertically */  
    justify-content: center; /* Center content vertically */
    border: 0.5px solid #ddd;  
    box-sizing: border-box;
}


.features-left-column p {
    color: white;
    font-size: 18px;
    font-weight: lighter;
    text-align: left;
    padding-left: 40px;
    padding-right: 40px;
    
}

.features-left-column h2 {
    color: white;
    font-size: 40px;
    font-weight: lighter;
    padding-left: 40px;
    padding-right: 40px;
  
}

.features-right-column {
    flex: 1; /* Takes up 1 part of the available space */
    min-height: 824px;
    display: flex; /* Enable flex for content centering */
    flex-direction: column; /* Stack content vertically */
    justify-content: center; /* Center content vertically */
    align-items: center; /* Center content horizontally */

    border: 0.5px solid #ddd;

    box-sizing: border-box;
}

/* Override global padding for p inside learnmore-right-column */
.features-right-column p {
    color: white;
    font-size: 18px;
    font-weight: lighter;
    text-align: left;
    padding-left: 40px;
    padding-right: 40px;
    transition: left ease-in 2s;
}
.features-right-column h3 {
    color: rgb(27, 116, 199);
    font-size: 20px;
    font-weight: lighter;
    text-align: left;
    padding-left: 40px;
    padding-right: 40px;
    transition: left ease-in 1.4s;
}
/* Make the columns stack on smaller screens */
@media (max-width: 768px) {
    .features-content-wrapper {
        flex-direction: column;
        gap: 20px; /* Adjust gap for stacked layout */
        align-items: stretch; /* Makes columns take full width when stacked */
    }

    .features-left-column,
    .features-right-column {
        flex-basis: auto; /* Reset flex-basis for stacked layout */
        width: 100%;
    }
}







.section-learnmore {
    /* Add padding if desired, e.g.: */
    min-height: 100h;
    width: 90%;
    margin: 0 auto;
    position: relative; /* Establish stacking context */
    z-index: 2;         /* Ensure it's above the hero section */
    background-color: #141416; /* Overall section background */
}

.learnmore-content-wrapper {
    display: flex;
    padding-top: 80px;
    
    align-items: center; /* Vertically aligns content if boxes have different heights */

    margin: 0 auto; /* Centers the wrapper */
}

.learnmore-left-column {
    flex: 1; /* Takes up 1 part of the available space */
    min-height: 824px;
    background-image: url('./images/section2.jpg');
    background-size: cover; /* Cover the entire section */
    background-position: center; /* Center the image */
    background-repeat: no-repeat; /* Do not repeat the image */
    display: flex; /* Enable flex for content centering */
    flex-direction: column; /* Stack content vertically */
    justify-content: center; /* Center content vertically */
    align-items: center; /* Center content horizontally */
    text-align: center; /* Center inline/text content */

    border: 0.5px solid #ddd;
   
    box-sizing: border-box;
}

.learnmore-left-column p {
    color: white;
    font-size: 18px;
    font-weight: lighter;
    text-align: left;
    padding-left: 40px;
    padding-right: 40px;
    
}

.learnmore-left-column h2 {
    color: white;
    font-size: 40px;
    font-weight: lighter;
    padding-left: 40px;
    padding-right: 40px;
  
}

.learnmore-right-column {
    flex: 1; /* Takes up 1 part of the available space */
    min-height: 824px;
    display: flex; /* Enable flex for content centering */
    flex-direction: column; /* Stack content vertically */
    justify-content: center; /* Center content vertically */
    align-items: center; /* Center content horizontally */
    text-align: center; /* Center inline/text content */
    border: 0.5px solid #ddd;
    
    box-sizing: border-box;
}

.learnmore-right-column p {
    color: white;
    font-size: 18px;
    font-weight: lighter;
    text-align: left;
    padding-left: 40px;
    padding-right: 40px;
    
}

.learnmore-right-column h2 {
    color: white;
    font-size: 40px;
    font-weight: lighter;
    padding-left: 40px;
    padding-right: 40px;
  
}
/* Make the columns stack on smaller screens */
@media (max-width: 768px) {
    .learnmore-content-wrapper {
        flex-direction: column;
        gap: 20px; /* Adjust gap for stacked layout */
        align-items: stretch; /* Makes columns take full width when stacked */
    }

    .learnmore-left-column,
    .learnmore-right-column {
        flex-basis: auto; /* Reset flex-basis for stacked layout */
        width: 100%;
    }
}

/* Optional: Style the button if it doesn't have global styles */
.utforsk-button {
    background-color: #b73a3a;
    border: none;
    border-radius: 5px;
    color:black;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin-left: 0; /* Reset for centering, flex align-items will handle it */
    margin-top: 40px;
    transition: background-color 0.3s ease;
    transition: left ease-in 1.4s;
}

.utforsk-button:hover {
    background-color: #000000;
    border: 1px solid white;
    color: white;
} 


.section-statistikk {
    padding-top: 40px;
    position: relative;
    border-style: solid;

    text-align: center;
    min-height: 30vh; /* Adjust min-height as content will now stack */
    
    
    color: #141416;
    z-index: 2;         /* Ensure it's above the hero section */
    background-color: #141416; /* Overall section background */
}

/* Target the h2 specifically within .stats for centering */
.stats h2 {
    /* Inherits color, font-size, font-weight from global h2 */
    text-align: center; /* Override global h2's text-align: left */
    padding-left: 0;    /* Override global h2's padding-left: 60px */
    margin-bottom: 10px; /* Add space below the title */
    width: 100%; /* Ensures the text centers within the available width */
}

.stats {
    display: flex;
    flex-direction: column; /* Stack h2 and stats-cards-container vertically */
    align-items: center;   /* Horizontally center the stacked children */
    
}

/* New container for the cards themselves */
.stats-cards-container {
    display: flex;
    flex-direction: row; /* Arrange cards horizontally */
    justify-content: space-around; /* Distribute cards with space around them */
    align-items: flex-start; /* Align cards to the top if they have different content heights */
    width: 100%; /* Take full width to allow space-around to work effectively */
    max-width: 800px;
    height: 120px;
    margin-top: 5px; /* Space between h2 and the cards */
    
}

/* Styling for the individual stat cards */
.stats .card-erfaring,
.stats .card-filer,
.stats .card-målinger {
    text-align: center;  /* Center the h4 and p text inside these cards */
    width: auto;         /* Allow cards to size to content, or set a max-width */
    /* max-width: 250px; /* Optional: if you want to constrain card width */
}

/* Override global padding and alignment for h4 and p within these specific cards */
.stats .card-erfaring h4, .stats .card-filer h4, .stats .card-målinger h4,
.stats .card-erfaring p, .stats .card-filer p, .stats .card-målinger p {
    /* margin-bottom on individual cards is removed, spacing handled by .stats-cards-container gap or justify-content */
    margin-bottom: 0; 
    padding-left: 0;
   
    text-align: center;
}
.section-filler {
    width: 100%;
    
    min-height: 300px;
    z-index: -1;
    background-image: url('./images/section3.jpg');
    /* Make the background image 150% of the container's width.
       Height will adjust to maintain aspect ratio.
       This ensures there's extra width to scroll through. */
    background-size: 200% auto;
    /* Set initial horizontal position to the left, vertically centered. */
    background-position: 0% 50%;
    background-repeat: no-repeat;
    /* background-attachment: scroll; is the default. 
       Removing 'fixed' allows JS to animate background-position relative to the element's scroll. */
}

.section-lastopp {
    width: 90%;
    margin: 0 auto;;
    padding-bottom: 40px;
    min-height: 80vh; /* Consistent with .section-learnmore */
    background-color: #141416; /* Base background for the section */
}

.lastopp-content-wrapper {
    display: flex;
    align-items: stretch; /* Makes columns equal height */
    
    margin: 0 auto; /* Centers the wrapper */
    
    /* gap: 20px; /* Optional: if you want space between columns, learnmore doesn't have it */
}

.lastopp { /* Styles for the left column */
    flex: 1;
    min-height: 800px; /* Consistent with .learnmore columns */
    
    background-image: url('./images/section4.jpg'); /* Moved image here */
    background-size: cover; /* Better for column background */
    background-repeat: no-repeat;
    
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    border: 0.5px solid #ddd; /* Consistent with .learnmore columns */
    
    box-sizing: border-box;
    color: white;
}

.lastopp-tekst { /* Styles for the right column */
    flex: 1;
    min-height: 800px; /* Consistent with .learnmore columns */
    
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    border: 0.5px solid #ddd; /* Consistent with .learnmore columns */
    padding: 20px;
    box-sizing: border-box;
    color: white;
}

/* Responsive adjustments for .section-lastopp, similar to .section-learnmore */
@media (max-width: 768px) {
    .lastopp-content-wrapper {
        flex-direction: column;
        
    }

    .lastopp,
    .lastopp-tekst {
        min-height: auto; /* Adjust height for stacked layout */
        border: none;
        background-image: none;
    }
}
/* Override global styles for h2 and p within .section-lastopp for centering */
.section-lastopp h2,
.section-lastopp p {
    padding-left: 0; /* Reset global padding */
    text-align: center; /* Ensure text is centered */
}

.lastopp-button {
    background-color: #ce2424;
    border: none;
    border-radius: 5px;
    color:black;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin-left: 0; /* Reset global margin for centering */
    margin-top: 40px;
    transition: background-color 0.3s ease;
    transition: left ease-in 1.4s;
}

.lastopp-button:hover {
    background-color: #000000;
    border: 1px solid white;
    color: white;
}
.footer {
    background-color: #000000;
    
    padding: 20px 0;
    text-align: center;
  
}
.footer p {
    font-size: 18px;
    text-align: center;
    padding-left: 0;
    margin: 0;
}